#!/usr/bin/env python3
"""
Direct Data Manager for Adventure Chess
Simple, direct JSON save/load without legacy migration or validation overhead
"""

import json
import os
import logging
from typing import Dict, Any, Optional, Tuple, List
from pathlib import Path

from config import PIECES_DIR, ABILITIES_DIR

logger = logging.getLogger(__name__)


class DirectDataManager:
    """
    Direct data manager that saves and loads JSON without migration or validation overhead
    """
    
    @staticmethod
    def save_piece(piece_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save piece data directly to JSON file
        
        Args:
            piece_data: Dictionary containing piece data
            filename: Optional filename (without .json extension)
        
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Determine filename
            if filename is None:
                filename = piece_data.get('name', 'unnamed_piece')
            
            # Clean filename
            filename = DirectDataManager._clean_filename(filename)
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(PIECES_DIR, filename)
            
            # Ensure directory exists
            os.makedirs(PIECES_DIR, exist_ok=True)
            
            # Add version if not present
            if 'version' not in piece_data:
                piece_data['version'] = '1.0.0'
            
            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(piece_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Piece saved successfully: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to save piece: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def load_piece(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load piece data directly from JSON file
        
        Args:
            filename: Filename (with or without .json extension)
        
        Returns:
            Tuple of (piece_data, error_message)
        """
        try:
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(PIECES_DIR, filename)
            
            # Check if file exists
            if not os.path.exists(file_path):
                error_msg = f"Piece file not found: {filename}"
                logger.error(error_msg)
                return None, error_msg
            
            # Load from file
            with open(file_path, 'r', encoding='utf-8') as f:
                piece_data = json.load(f)
            
            logger.info(f"Piece loaded successfully: {file_path}")
            return piece_data, None
            
        except Exception as e:
            error_msg = f"Failed to load piece: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    @staticmethod
    def save_ability(ability_data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability data directly to JSON file
        
        Args:
            ability_data: Dictionary containing ability data
            filename: Optional filename (without .json extension)
        
        Returns:
            Tuple of (success, error_message)
        """
        try:
            # Determine filename
            if filename is None:
                filename = ability_data.get('name', 'unnamed_ability')
            
            # Clean filename
            filename = DirectDataManager._clean_filename(filename)
            
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(ABILITIES_DIR, filename)
            
            # Ensure directory exists
            os.makedirs(ABILITIES_DIR, exist_ok=True)
            
            # Add version if not present
            if 'version' not in ability_data:
                ability_data['version'] = '1.0.0'
            
            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(ability_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Ability saved successfully: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to save ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def load_ability(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load ability data directly from JSON file
        
        Args:
            filename: Filename (with or without .json extension)
        
        Returns:
            Tuple of (ability_data, error_message)
        """
        try:
            # Ensure .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Full file path
            file_path = os.path.join(ABILITIES_DIR, filename)
            
            # Check if file exists
            if not os.path.exists(file_path):
                error_msg = f"Ability file not found: {filename}"
                logger.error(error_msg)
                return None, error_msg
            
            # Load from file
            with open(file_path, 'r', encoding='utf-8') as f:
                ability_data = json.load(f)
            
            logger.info(f"Ability loaded successfully: {file_path}")
            return ability_data, None
            
        except Exception as e:
            error_msg = f"Failed to load ability: {str(e)}"
            logger.error(error_msg)
            return None, error_msg
    
    @staticmethod
    def list_pieces() -> List[str]:
        """Get list of available piece files (without .json extension)"""
        try:
            if not os.path.exists(PIECES_DIR):
                return []
            
            pieces = []
            for filename in os.listdir(PIECES_DIR):
                if filename.endswith('.json'):
                    pieces.append(filename[:-5])  # Remove .json extension
            
            return sorted(pieces)
            
        except Exception as e:
            logger.error(f"Failed to list pieces: {str(e)}")
            return []
    
    @staticmethod
    def list_abilities() -> List[str]:
        """Get list of available ability files (without .json extension)"""
        try:
            if not os.path.exists(ABILITIES_DIR):
                return []
            
            abilities = []
            for filename in os.listdir(ABILITIES_DIR):
                if filename.endswith('.json'):
                    abilities.append(filename[:-5])  # Remove .json extension
            
            return sorted(abilities)
            
        except Exception as e:
            logger.error(f"Failed to list abilities: {str(e)}")
            return []
    
    @staticmethod
    def delete_piece(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete a piece file"""
        try:
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = os.path.join(PIECES_DIR, filename)
            
            if not os.path.exists(file_path):
                return False, f"Piece file not found: {filename}"
            
            os.remove(file_path)
            logger.info(f"Piece deleted: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to delete piece: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def delete_ability(filename: str) -> Tuple[bool, Optional[str]]:
        """Delete an ability file"""
        try:
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = os.path.join(ABILITIES_DIR, filename)
            
            if not os.path.exists(file_path):
                return False, f"Ability file not found: {filename}"
            
            os.remove(file_path)
            logger.info(f"Ability deleted: {file_path}")
            return True, None
            
        except Exception as e:
            error_msg = f"Failed to delete ability: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @staticmethod
    def _clean_filename(filename: str) -> str:
        """Clean filename for safe file system usage"""
        # Remove invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing whitespace and dots
        filename = filename.strip(' .')
        
        # Ensure not empty
        if not filename:
            filename = 'unnamed'
        
        return filename
