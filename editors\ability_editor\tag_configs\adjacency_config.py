"""
Adjacency tag configuration for ability editor.
Handles adjacency-based ability configurations with inline 3x3 tile selection grid.
"""

from PyQt6.QtWidgets import (
    QPushButton, QLabel, QWidget, QVBoxLayout, QGridLayout, QFormLayout
)
from PyQt6.QtCore import Qt
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class AdjacencyConfig(BaseTagConfig):
    """Configuration for adjacency tag abilities matching old editor exactly."""

    def __init__(self, editor):
        super().__init__(editor, "adjacencyRequired")
        # Initialize adjacency tile pattern (3x3 grid)
        self.adjacency_tile_pattern = [[False for _ in range(3)] for _ in range(3)]
        self.adjacency_tile_buttons = []
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for adjacency configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting adjacency UI creation")

            # Main layout
            layout = QVBoxLayout()

            # Description
            description = QLabel("Only activates if adjacent to defined units.")
            description.setWordWrap(True)
            description.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
            layout.addWidget(description)

            # Tile selection section
            tile_layout = QVBoxLayout()
            tile_label = QLabel("Specific Adjacent Tiles (relative to piece):")
            tile_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
            tile_layout.addWidget(tile_label)

            # Create 3x3 grid for adjacency positions (matching old editor exactly)
            adjacency_tile_widget = QWidget()
            adjacency_tile_widget.setFixedSize(120, 120)
            tile_grid_layout = QGridLayout(adjacency_tile_widget)
            tile_grid_layout.setSpacing(2)

            self.adjacency_tile_buttons = []
            for r in range(3):
                row = []
                for c in range(3):
                    btn = QPushButton()
                    btn.setFixedSize(35, 35)
                    btn.setCheckable(True)
                    if r == 1 and c == 1:  # Center (piece position)
                        btn.setText("♔")
                        btn.setEnabled(False)
                        btn.setStyleSheet("background: #3399ff; color: white; font-weight: bold;")
                    else:
                        btn.clicked.connect(lambda checked, row=r, col=c: self.toggle_adjacency_tile(row, col, checked))
                        btn.setStyleSheet("background: #f0f0f0; border: 1px solid #ccc;")
                    tile_grid_layout.addWidget(btn, r, c)
                    row.append(btn)
                self.adjacency_tile_buttons.append(row)

            self.store_widget("adjacency_tile_widget", adjacency_tile_widget)
            tile_layout.addWidget(adjacency_tile_widget)
            layout.addLayout(tile_layout)

            # Add to parent layout
            parent_layout.addLayout(layout)

            # Initialize display
            self.update_adjacency_tile_display()

            self.log_debug("Adjacency UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def toggle_adjacency_tile(self, row, col, checked):
        """Toggle adjacency tile selection matching old editor exactly."""
        try:
            if hasattr(self, 'adjacency_tile_pattern'):
                self.adjacency_tile_pattern[row][col] = checked
                self.update_adjacency_tile_display()

                # Mark as changed
                if hasattr(self.editor, 'mark_unsaved_changes'):
                    self.editor.mark_unsaved_changes()

                self.log_debug(f"Adjacency tile toggled at ({row}, {col}): {checked}")

        except Exception as e:
            self.log_error(f"Error toggling adjacency tile: {e}")

    def update_adjacency_tile_display(self):
        """Update adjacency tile display matching old editor exactly."""
        try:
            if not hasattr(self, 'adjacency_tile_buttons') or not hasattr(self, 'adjacency_tile_pattern'):
                return

            for r in range(3):
                for c in range(3):
                    if r == 1 and c == 1:  # Skip center (piece position)
                        continue
                    btn = self.adjacency_tile_buttons[r][c]
                    if self.adjacency_tile_pattern[r][c]:
                        btn.setStyleSheet("background: #ffcc66; border: 2px solid #cc8800; font-weight: bold;")
                        btn.setText("✓")
                        btn.setChecked(True)
                    else:
                        btn.setStyleSheet("background: #f0f0f0; border: 1px solid #ccc;")
                        btn.setText("")
                        btn.setChecked(False)

        except Exception as e:
            self.log_error(f"Error updating adjacency tile display: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting adjacency data population with: {data}")

            # Load adjacency tile pattern
            if 'adjacencyTilePattern' in data:
                pattern = data['adjacencyTilePattern']
                if pattern and len(pattern) == 3 and len(pattern[0]) == 3:
                    self.adjacency_tile_pattern = [row[:] for row in pattern]  # Deep copy
                else:
                    self.adjacency_tile_pattern = [[False for _ in range(3)] for _ in range(3)]
            else:
                self.adjacency_tile_pattern = [[False for _ in range(3)] for _ in range(3)]

            # Update display
            self.update_adjacency_tile_display()

            self.log_debug("Adjacency data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the adjacency configuration data
        """
        try:
            data = {}

            # Save adjacency tile pattern
            data["adjacencyTilePattern"] = [row[:] for row in self.adjacency_tile_pattern]  # Deep copy

            self.log_debug(f"Collected adjacency data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
