#!/usr/bin/env python3
"""
Test Suite for Enhanced Error Handling System
Validates error handling, recovery mechanisms, and user feedback
"""

import os
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any

from enhanced_error_handling import (
    error_handler, ErrorSeverity, <PERSON>rror<PERSON>ategory, ErrorContext,
    safe_file_load, safe_file_save
)
from error_handling_integration import EnhancedDirectDataManager, EnhancedSimpleBridge

def test_basic_error_handling():
    """Test basic error handling functionality"""
    print("Testing basic error handling...")
    
    # Test handling a simple exception
    try:
        raise ValueError("Test error message")
    except Exception as e:
        error_context = error_handler.handle_error(
            error=e,
            severity=ErrorSeverity.WARNING,
            category=ErrorCategory.DATA_VALIDATION,
            operation="test_operation"
        )
        
        assert error_context.severity == ErrorSeverity.WARNING
        assert error_context.category == ErrorCategory.DATA_VALIDATION
        assert "Test error message" in error_context.message
        assert error_context.operation == "test_operation"
        assert error_context.recovery_suggestions is not None
        assert error_context.user_message is not None
        
        print("✓ Basic error handling works correctly")

def test_file_operation_error_handling():
    """Test file operation specific error handling"""
    print("Testing file operation error handling...")
    
    # Test loading non-existent file
    data, error_message = safe_file_load("non_existent_file.json")
    assert data is None
    assert error_message is not None
    print(f"Error message received: '{error_message}'")
    # Check for various possible error messages
    error_lower = error_message.lower()
    assert ("not found" in error_lower or
            "no such file" in error_lower or
            "could not be found" in error_lower or
            "file appears to be corrupted" in error_lower)
    print("✓ Non-existent file error handled correctly")
    
    # Test saving to invalid path (if possible)
    invalid_data = {"test": "data"}
    success, error_message = safe_file_save(invalid_data, "/invalid/path/file.json")
    if not success:
        assert error_message is not None
        print("✓ Invalid path error handled correctly")
    else:
        print("✓ File save succeeded (path was valid)")

def test_enhanced_data_manager():
    """Test enhanced data manager functionality"""
    print("Testing enhanced data manager...")
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        # Mock config for testing
        import sys
        import types
        config_module = types.ModuleType('config')
        config_module.PIECES_DIR = os.path.join(temp_dir, 'pieces')
        config_module.ABILITIES_DIR = os.path.join(temp_dir, 'abilities')
        sys.modules['config'] = config_module
        
        # Ensure directories exist
        os.makedirs(config_module.PIECES_DIR, exist_ok=True)
        os.makedirs(config_module.ABILITIES_DIR, exist_ok=True)
        
        # Test piece operations
        test_piece_data = {
            "name": "Test Piece",
            "description": "A test piece",
            "role": "Commander",
            "movement": {"type": "orthogonal", "distance": 3},
            "canCapture": True,
            "abilities": []
        }
        
        # Test save piece
        success, error = EnhancedDirectDataManager.save_piece(test_piece_data, "test_piece")
        assert success, f"Failed to save piece: {error}"
        print("✓ Piece save works correctly")
        
        # Test load piece
        loaded_data, error = EnhancedDirectDataManager.load_piece("test_piece")
        assert loaded_data is not None, f"Failed to load piece: {error}"
        assert loaded_data["name"] == "Test Piece"
        assert "version" in loaded_data  # Should be added automatically
        print("✓ Piece load works correctly")
        
        # Test list pieces
        pieces = EnhancedDirectDataManager.list_pieces()
        assert "test_piece" in pieces
        print("✓ Piece listing works correctly")
        
        # Test ability operations
        test_ability_data = {
            "name": "Test Ability",
            "description": "A test ability",
            "cost": 2,
            "tags": ["move", "range"]
        }
        
        # Test save ability
        success, error = EnhancedDirectDataManager.save_ability(test_ability_data, "test_ability")
        assert success, f"Failed to save ability: {error}"
        print("✓ Ability save works correctly")
        
        # Test load ability
        loaded_data, error = EnhancedDirectDataManager.load_ability("test_ability")
        assert loaded_data is not None, f"Failed to load ability: {error}"
        assert loaded_data["name"] == "Test Ability"
        assert "version" in loaded_data  # Should be added automatically
        print("✓ Ability load works correctly")
        
        # Test list abilities
        abilities = EnhancedDirectDataManager.list_abilities()
        assert "test_ability" in abilities
        print("✓ Ability listing works correctly")

def test_error_recovery_suggestions():
    """Test error recovery suggestion generation"""
    print("Testing error recovery suggestions...")
    
    # Test file operation error suggestions
    try:
        with open("/invalid/path/file.json", 'r') as f:
            pass
    except Exception as e:
        error_context = error_handler.handle_file_operation_error(
            error=e,
            operation="test_file_read",
            file_path="/invalid/path/file.json"
        )
        
        assert error_context.recovery_suggestions is not None
        assert len(error_context.recovery_suggestions) > 0
        print("✓ File operation recovery suggestions generated")
    
    # Test validation error suggestions
    try:
        raise ValueError("Invalid data format")
    except Exception as e:
        error_context = error_handler.handle_validation_error(
            error=e,
            data={"invalid": "data"}
        )
        
        assert error_context.recovery_suggestions is not None
        assert len(error_context.recovery_suggestions) > 0
        print("✓ Validation error recovery suggestions generated")

def test_error_summary():
    """Test error summary functionality"""
    print("Testing error summary...")
    
    # Generate some test errors
    for i in range(3):
        try:
            raise ValueError(f"Test error {i}")
        except Exception as e:
            error_handler.handle_error(
                error=e,
                severity=ErrorSeverity.WARNING,
                category=ErrorCategory.DATA_VALIDATION
            )
    
    # Get error summary
    summary = error_handler.get_error_summary()
    
    assert "total_errors" in summary
    assert summary["total_errors"] >= 3
    assert "by_severity" in summary
    assert "by_category" in summary
    assert "recent_errors" in summary
    
    print("✓ Error summary generation works correctly")

def test_safe_operations():
    """Test safe operation wrappers"""
    print("Testing safe operations...")
    
    # Test safe file operation with success
    def successful_operation():
        return {"result": "success"}
    
    result, error_context = error_handler.safe_file_operation(
        operation=successful_operation,
        operation_name="test_success",
        fallback_result=None
    )
    
    assert result == {"result": "success"}
    assert error_context is None
    print("✓ Safe operation with success works correctly")
    
    # Test safe file operation with failure
    def failing_operation():
        raise ValueError("Operation failed")
    
    result, error_context = error_handler.safe_file_operation(
        operation=failing_operation,
        operation_name="test_failure",
        fallback_result={"fallback": True}
    )
    
    assert result == {"fallback": True}
    assert error_context is not None
    assert error_context.message == "Operation failed"
    print("✓ Safe operation with failure works correctly")

def test_filename_cleaning():
    """Test filename cleaning functionality"""
    print("Testing filename cleaning...")
    
    # Test various problematic filenames
    test_cases = [
        ("normal_file", "normal_file"),
        ("file with spaces", "file with spaces"),
        ("file<>:with|bad*chars", "file___with_bad_chars"),
        ("  .leading_trailing.  ", "leading_trailing"),
        ("", "unnamed_file"),
        ("file/with\\slashes", "file_with_slashes")
    ]
    
    for input_name, expected in test_cases:
        cleaned = EnhancedDirectDataManager._clean_filename(input_name)
        # Basic validation - should not contain invalid characters
        invalid_chars = '<>:"/\\|?*'
        assert not any(char in cleaned for char in invalid_chars), f"Cleaned filename '{cleaned}' still contains invalid characters"
        print(f"✓ Filename '{input_name}' cleaned to '{cleaned}'")

def run_all_tests():
    """Run all error handling tests"""
    print("=" * 60)
    print("ENHANCED ERROR HANDLING SYSTEM TESTS")
    print("=" * 60)
    
    try:
        test_basic_error_handling()
        test_file_operation_error_handling()
        test_enhanced_data_manager()
        test_error_recovery_suggestions()
        test_error_summary()
        test_safe_operations()
        test_filename_cleaning()
        
        print("\n" + "=" * 60)
        print("ALL TESTS PASSED SUCCESSFULLY!")
        print("Enhanced error handling system is working correctly.")
        print("=" * 60)
        
        # Print error summary
        summary = error_handler.get_error_summary()
        print(f"\nTest run generated {summary['total_errors']} total errors (expected for testing)")
        print("Error breakdown by severity:")
        for severity, count in summary['by_severity'].items():
            if count > 0:
                print(f"  {severity}: {count}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    exit(0 if success else 1)
