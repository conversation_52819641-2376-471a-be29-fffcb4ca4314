#!/usr/bin/env python3
# Version field repair script for Adventure Chess Creator
import json
from pathlib import Path

def repair_version_fields():
    """Add version fields to files missing them"""

    # Files missing version fields from audit
    missing_version_files = []


    for file_path in missing_version_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Add version field if missing
            if "version" not in data:
                data["version"] = "1.0.0"

                # Create backup
                backup_path = file_path + ".backup"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)

                # Save repaired file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)

                print(f"Repaired version field in: {file_path}")

        except Exception as e:
            print(f"Error repairing {file_path}: {e}")

if __name__ == "__main__":
    repair_version_fields()
