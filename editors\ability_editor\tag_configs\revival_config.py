"""
Revival tag configuration for ability editor.
Handles revival-based ability configurations.
"""

from PyQt6.QtWidgets import QSpin<PERSON>ox, QCheckBox, QWidget
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector


class RevivalConfig(BaseTagConfig):
    """Configuration for revival tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "revival")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for revival configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting revival UI creation")
            
            # Create form layout
            form_layout = self.create_form_layout()
            self.log_debug("Created form layout")
            
            # Revival pieces selector (InlinePieceSelector)
            revival_selector = InlinePieceSelector(
                parent=self.editor,
                title="Revival Targets",
                allow_costs=True
            )
            self.store_widget("revival_piece_selector", revival_selector)
            form_layout.addRow("Pieces to Revive:", revival_selector)
            self.log_debug("Added revival piece selector")
            
            # Maximum revivals per use spinner (1-10)
            revival_max = QSpinBox()
            revival_max.setRange(1, 10)
            revival_max.setValue(1)
            revival_max.setToolTip("Maximum number of pieces that can be revived per use")
            self.store_widget("revival_max_spinner", revival_max)
            self.connect_change_signals(revival_max)
            form_layout.addRow("Max Revivals:", revival_max)
            self.log_debug("Added revival max spinner")
            
            # Revival health percentage spinner (1-100%)
            revival_health = QSpinBox()
            revival_health.setRange(1, 100)
            revival_health.setValue(100)
            revival_health.setSuffix("%")
            revival_health.setToolTip("Percentage of health restored when revived")
            self.store_widget("revival_health_spinner", revival_health)
            self.connect_change_signals(revival_health)
            form_layout.addRow("Revival Health:", revival_health)
            self.log_debug("Added revival health spinner")
            
            # Restore abilities checkbox
            restore_abilities = QCheckBox("Restore abilities on revival")
            restore_abilities.setChecked(True)
            restore_abilities.setToolTip("Whether revived pieces retain their abilities")
            self.store_widget("revival_restore_abilities", restore_abilities)
            self.connect_change_signals(restore_abilities)
            form_layout.addRow("", restore_abilities)
            self.log_debug("Added restore abilities checkbox")
            
            # Can revive enemy pieces checkbox
            revive_enemies = QCheckBox("Can revive enemy pieces")
            revive_enemies.setToolTip("Whether the ability can revive enemy pieces")
            self.store_widget("revival_revive_enemies", revive_enemies)
            self.connect_change_signals(revive_enemies)
            form_layout.addRow("", revive_enemies)
            self.log_debug("Added revive enemies checkbox")
            
            # Add the form layout to the parent
            form_widget = QWidget()
            form_widget.setLayout(form_layout)
            parent_layout.addWidget(form_widget)
            
            self.log_debug("Revival UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting revival data population with: {data}")
            
            # Populate revival pieces list
            revival_selector = self.get_widget_by_name("revival_piece_selector")
            if revival_selector:
                revival_list = data.get("revivalTargets", [])
                self.log_debug(f"Setting revival list to: {revival_list}")
                revival_selector.set_pieces(revival_list)
            
            # Populate revival max
            revival_max = self.get_widget_by_name("revival_max_spinner")
            if revival_max:
                max_value = data.get("revivalMax", 1)
                self.log_debug(f"Setting revival max to: {max_value}")
                revival_max.setValue(max_value)
            
            # Populate revival health
            revival_health = self.get_widget_by_name("revival_health_spinner")
            if revival_health:
                health_value = data.get("revivalHealth", 100)
                self.log_debug(f"Setting revival health to: {health_value}")
                revival_health.setValue(health_value)
            
            # Populate restore abilities
            restore_abilities = self.get_widget_by_name("revival_restore_abilities")
            if restore_abilities:
                restore_value = data.get("revivalRestoreAbilities", True)
                self.log_debug(f"Setting restore abilities to: {restore_value}")
                restore_abilities.setChecked(restore_value)
            
            # Populate revive enemies
            revive_enemies = self.get_widget_by_name("revival_revive_enemies")
            if revive_enemies:
                enemies_value = data.get("revivalReviveEnemies", False)
                self.log_debug(f"Setting revive enemies to: {enemies_value}")
                revive_enemies.setChecked(enemies_value)
            
            self.log_debug("Revival data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the revival configuration data
        """
        try:
            data = {}
            
            # Collect revival pieces list
            revival_selector = self.get_widget_by_name("revival_piece_selector")
            if revival_selector:
                pieces_list = revival_selector.get_pieces()
                data["revivalTargets"] = pieces_list
            
            # Collect revival max
            revival_max = self.get_widget_by_name("revival_max_spinner")
            if revival_max:
                data["revivalMax"] = revival_max.value()
            
            # Collect revival health
            revival_health = self.get_widget_by_name("revival_health_spinner")
            if revival_health:
                data["revivalHealth"] = revival_health.value()
            
            # Collect restore abilities
            restore_abilities = self.get_widget_by_name("revival_restore_abilities")
            if restore_abilities:
                data["revivalRestoreAbilities"] = restore_abilities.isChecked()
            
            # Collect revive enemies
            revive_enemies = self.get_widget_by_name("revival_revive_enemies")
            if revive_enemies:
                data["revivalReviveEnemies"] = revive_enemies.isChecked()
            
            self.log_debug(f"Collected revival data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
