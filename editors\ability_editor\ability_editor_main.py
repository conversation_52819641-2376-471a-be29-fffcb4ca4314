"""
Main Ability Editor for Adventure Chess Creator

This is the refactored main editor class that integrates all the separated components:
- AbilityDataHandler: Handles all data operations
- AbilityUIComponents: Manages UI creation and layout
- AbilityTagManager: Handles tag selection and configuration

This refactoring improves maintainability by:
1. Separating concerns into focused modules
2. Making data handling easier to debug
3. Simplifying UI management
4. Making tag configuration more modular
"""

import logging
import os
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import QWidget, QFileDialog, QMessageBox
from PyQt6.QtCore import Qt

# Local imports
from editors.base_editor import BaseEditor
from config import ABILITIES_DIR, ABILITY_EDITOR_DEFAULT, ABILITY_EDITOR_MIN
from ui.ui_utils import setup_responsive_window
from utils.simple_bridge import simple_bridge

# Import refactored components
from .ability_data_handlers import AbilityDataHandler
from .ability_ui_components import AbilityUIComponents
from .ability_tag_managers import AbilityTagManager

logger = logging.getLogger(__name__)


class AbilityEditorWindow(BaseEditor):
    """
    Adventure Chess Ability Editor - Refactored Version
    
    This class now focuses on coordination between components rather than
    implementing everything directly. The actual functionality is delegated
    to specialized handler classes.
    
    Components:
    - data_handler: Manages all data operations (load/save/validate)
    - ui_components: Handles UI creation and layout
    - tag_manager: Manages tag selection and configuration
    """
    
    def __init__(self):
        """Initialize the ability editor with refactored components."""
        # Initialize base editor with ability data type
        super().__init__("ability")
        
        self.setWindowTitle("Ability Editor - Adventure Chess")
        
        # Setup responsive window
        setup_responsive_window(self, ABILITY_EDITOR_DEFAULT, ABILITY_EDITOR_MIN)
        
        # Legacy compatibility - redirect to base class properties
        self.current_ability = self.current_data
        
        # Initialize refactored components
        self.data_handler = AbilityDataHandler(self)
        self.ui_components = AbilityUIComponents(self)
        self.tag_manager = AbilityTagManager(self)
        
        # Initialize UI and reset form
        self.init_ui()
        self.reset_form()
        
        logger.info("Ability editor initialized successfully")
    
    def init_ui(self) -> None:
        """Initialize the user interface using the UI components handler."""
        try:
            central_widget = self.ui_components.init_main_ui()
            self.setCentralWidget(central_widget)
            
            # Setup the tags UI in the tags tab
            self.tag_manager.create_tags_ui(self.tags_layout)
            
            # Setup change tracking
            self.setup_change_tracking()
            
            # Refresh file lists
            self.refresh_file_lists()
            
        except Exception as e:
            logger.error(f"Error initializing UI: {e}")
            raise
    
    def setup_change_tracking(self) -> None:
        """Setup automatic change tracking for UI widgets."""
        try:
            # Delegate to UI components if the method exists
            if hasattr(self.ui_components, 'setup_change_tracking'):
                self.ui_components.setup_change_tracking()
            else:
                # Basic change tracking setup
                logger.info("Basic change tracking setup - detailed tracking not implemented yet")

        except Exception as e:
            logger.error(f"Error setting up change tracking: {e}")
    
    # ========== DATA OPERATIONS (delegated to data handler) ==========
    
    def collect_widget_data(self) -> Dict[str, Any]:
        """Collect data from UI widgets using the data handler."""
        return self.data_handler.collect_ability_data()
    
    def set_widget_values_from_data(self, data: Dict[str, Any]) -> None:
        """Set widget values from data using the data handler."""
        self.data_handler.populate_ability_data(data)
    
    def set_ability_data(self, data: Dict[str, Any]) -> None:
        """Set form data from ability data (legacy compatibility)."""
        self.data_handler.populate_ability_data(data)
    
    def validate_ability_data(self, data: Dict[str, Any]) -> list:
        """Validate ability data using the data handler."""
        return self.data_handler.validate_ability_data(data)
    
    # ========== FILE OPERATIONS ==========
    
    def new_ability(self) -> None:
        """Create a new ability."""
        try:
            if self.unsaved_changes:
                reply = QMessageBox.question(
                    self, "Unsaved Changes",
                    "You have unsaved changes. Do you want to continue?",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )
                if reply == QMessageBox.StandardButton.No:
                    return
            
            self.reset_form()
            logger.info("Created new ability")
            
        except Exception as e:
            logger.error(f"Error creating new ability: {e}")
            QMessageBox.critical(self, "Error", f"Failed to create new ability: {str(e)}")
    
    def load_ability(self) -> None:
        """Load an ability from file."""
        try:
            filename, _ = QFileDialog.getOpenFileName(
                self, "Load Ability", ABILITIES_DIR, "JSON Files (*.json)"
            )
            
            if filename:
                base_filename = os.path.basename(filename).replace('.json', '')
                success, error = self.data_handler.load_ability_from_file(base_filename)
                
                if success:
                    self.status_widget.show_success(f"Loaded ability: {base_filename}")
                    self.refresh_file_lists()
                else:
                    QMessageBox.critical(self, "Error", f"Failed to load ability:\n{error}")
                    
        except Exception as e:
            logger.error(f"Error loading ability: {e}")
            QMessageBox.critical(self, "Error", f"Failed to load ability: {str(e)}")
    
    def save_ability(self) -> bool:
        """Save the current ability."""
        try:
            success, error = self.data_handler.save_ability_to_file(self.current_filename)
            
            if success:
                ability_name = self.data_handler.get_current_ability_name()
                self.status_widget.show_success(f"Saved ability: {ability_name}")
                self.refresh_file_lists()
                return True
            else:
                QMessageBox.critical(self, "Error", f"Failed to save ability:\n{error}")
                return False
                
        except Exception as e:
            logger.error(f"Error saving ability: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save ability: {str(e)}")
            return False
    
    def save_as_ability(self) -> bool:
        """Save the ability with a new filename."""
        try:
            filename, _ = QFileDialog.getSaveFileName(
                self, "Save Ability As", ABILITIES_DIR, "JSON Files (*.json)"
            )
            
            if filename:
                base_filename = os.path.basename(filename).replace('.json', '')
                success, error = self.data_handler.save_ability_to_file(base_filename)
                
                if success:
                    self.current_filename = base_filename
                    self.status_widget.show_success(f"Saved ability as: {base_filename}")
                    self.refresh_file_lists()
                    return True
                else:
                    QMessageBox.critical(self, "Error", f"Failed to save ability:\n{error}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error saving ability as: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save ability: {str(e)}")
            return False
    
    def delete_current_ability(self) -> None:
        """Delete the current ability file."""
        try:
            if not self.current_filename:
                QMessageBox.warning(self, "Warning", "No ability loaded to delete.")
                return
            
            reply = QMessageBox.question(
                self, "Confirm Delete",
                f"Are you sure you want to delete '{self.current_filename}'?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                file_path = os.path.join(ABILITIES_DIR, f"{self.current_filename}.json")
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self.status_widget.show_success(f"Deleted ability: {self.current_filename}")
                    self.reset_form()
                    self.refresh_file_lists()
                else:
                    QMessageBox.warning(self, "Warning", "File not found.")
                    
        except Exception as e:
            logger.error(f"Error deleting ability: {e}")
            QMessageBox.critical(self, "Error", f"Failed to delete ability: {str(e)}")
    
    # ========== UI EVENT HANDLERS ==========
    
    def on_load_selection_changed(self, text: str) -> None:
        """Handle load combo selection changes."""
        try:
            if text and text.strip():
                # Remove .json extension if present
                filename = text.replace('.json', '')
                success, error = self.data_handler.load_ability_from_file(filename)
                
                if success:
                    self.status_widget.show_success(f"Loaded ability: {filename}")
                else:
                    self.status_widget.show_error(f"Failed to load: {error}")
                    
        except Exception as e:
            logger.error(f"Error handling load selection: {e}")
    
    def on_tag_changed(self) -> None:
        """Handle tag changes (delegated to tag manager)."""
        self.tag_manager.on_tag_changed()
    
    def update_configuration_ui(self) -> None:
        """Update configuration UI (delegated to tag manager)."""
        self.tag_manager.update_configuration_ui()
    
    # ========== ABSTRACT METHOD IMPLEMENTATIONS ==========
    
    def reset_form(self) -> None:
        """Reset form to default values."""
        self.data_handler.reset_to_defaults()
    
    def post_load_update(self) -> None:
        """Update UI components after loading data."""
        try:
            self.ui_components.refresh_ui_state()
            self.tag_manager.update_configuration_ui()
            
        except Exception as e:
            logger.error(f"Error in post-load update: {e}")
    
    def post_save_update(self) -> None:
        """Update UI components after saving data."""
        try:
            self.ui_components.refresh_ui_state()
            
        except Exception as e:
            logger.error(f"Error in post-save update: {e}")
    
    def refresh_file_lists(self) -> None:
        """Refresh file lists/dropdowns."""
        try:
            if hasattr(self, 'load_combo'):
                current_text = self.load_combo.currentText()
                self.load_combo.clear()
                
                # Get list of abilities
                abilities = simple_bridge.list_abilities()
                self.load_combo.addItems(abilities)
                
                # Restore selection if it still exists
                if current_text in abilities:
                    self.load_combo.setCurrentText(current_text)
                    
        except Exception as e:
            logger.error(f"Error refreshing file lists: {e}")
    
    # ========== LEGACY COMPATIBILITY METHODS ==========
    
    def save_data(self, filename: Optional[str] = None) -> bool:
        """Save data (BaseEditor abstract method)."""
        if filename:
            return self.data_handler.save_ability_to_file(filename)[0]
        return self.save_ability()

    def save_as_data(self, filename: Optional[str] = None) -> bool:
        """Save data with new filename (BaseEditor abstract method)."""
        if filename:
            return self.data_handler.save_ability_to_file(filename)[0]
        return self.save_as_ability()
    
    def load_ability_by_filename(self, filename: str) -> None:
        """Load ability by filename (legacy compatibility)."""
        success, error = self.data_handler.load_ability_from_file(filename)
        if not success:
            QMessageBox.critical(self, "Error", f"Failed to load ability:\n{error}")


# For backward compatibility, export the main class
__all__ = ['AbilityEditorWindow']
