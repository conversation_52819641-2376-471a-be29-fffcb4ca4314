"""
NoTurnCost tag configuration for ability editor.
Handles no turn cost ability configurations.
"""

from PyQt6.QtWidgets import (QFormLayout, QCheckBox, QSpinBox, QComboBox,
                            QWidget, QLabel)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class NoTurnCostConfig(BaseTagConfig):
    """Configuration for noTurnCost tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "noTurnCost")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for no turn cost configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting no turn cost UI creation")
            
            # Create main widget
            main_widget = QWidget()
            form_layout = QFormLayout()
            
            # Info label
            info_label = QLabel("Configure when this ability doesn't consume a turn:")
            info_label.setWordWrap(True)
            info_label.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
            form_layout.addRow(info_label)
            
            # Always no turn cost
            self.always_no_cost_check = QCheckBox("Never consumes a turn")
            form_layout.addRow("Always Free:", self.always_no_cost_check)
            self.store_widget("always_no_cost", self.always_no_cost_check)
            
            # Conditional no turn cost
            self.conditional_no_cost_check = QCheckBox("Conditional no turn cost")
            form_layout.addRow("Conditional:", self.conditional_no_cost_check)
            self.store_widget("conditional_no_cost", self.conditional_no_cost_check)
            
            # Condition type
            self.condition_type_combo = QComboBox()
            self.condition_type_combo.addItems([
                "On Kill", "On Capture", "On Miss", "On Hit",
                "First Use", "Low Health", "Adjacent Ally", "No Enemies Nearby"
            ])
            form_layout.addRow("Condition:", self.condition_type_combo)
            self.store_widget("condition_type", self.condition_type_combo)
            
            # Uses per turn
            self.uses_per_turn_spin = QSpinBox()
            self.uses_per_turn_spin.setRange(1, 10)
            self.uses_per_turn_spin.setValue(1)
            self.uses_per_turn_spin.setSuffix(" free uses per turn")
            form_layout.addRow("Free Uses:", self.uses_per_turn_spin)
            self.store_widget("uses_per_turn", self.uses_per_turn_spin)
            
            # Cooldown after free use
            self.cooldown_after_free_check = QCheckBox("Apply cooldown after free use")
            form_layout.addRow("Cooldown:", self.cooldown_after_free_check)
            self.store_widget("cooldown_after_free", self.cooldown_after_free_check)
            
            # Stacks with other abilities
            self.stacks_check = QCheckBox("Stacks with other no-cost abilities")
            form_layout.addRow("Stacks:", self.stacks_check)
            self.store_widget("stacks", self.stacks_check)
            
            # Only on successful activation
            self.success_only_check = QCheckBox("Only on successful activation")
            form_layout.addRow("Success Only:", self.success_only_check)
            self.store_widget("success_only", self.success_only_check)
            
            main_widget.setLayout(form_layout)
            parent_layout.addWidget(main_widget)
            
            # Connect change signals
            self.connect_change_signals()
            
            self.log_debug("No turn cost UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting no turn cost data population with: {data}")
            
            # Always no turn cost
            always_check = self.get_widget_by_name("always_no_cost")
            if always_check and "noTurnCostAlways" in data:
                always_check.setChecked(data["noTurnCostAlways"])
            
            # Conditional no turn cost
            conditional_check = self.get_widget_by_name("conditional_no_cost")
            if conditional_check and "noTurnCostConditional" in data:
                conditional_check.setChecked(data["noTurnCostConditional"])
            
            # Condition type
            condition_combo = self.get_widget_by_name("condition_type")
            if condition_combo and "noTurnCostCondition" in data:
                condition = data["noTurnCostCondition"]
                index = condition_combo.findText(condition)
                if index >= 0:
                    condition_combo.setCurrentIndex(index)
            
            # Uses per turn
            uses_spin = self.get_widget_by_name("uses_per_turn")
            if uses_spin and "noTurnCostUses" in data:
                uses_spin.setValue(data["noTurnCostUses"])
            
            # Cooldown after free use
            cooldown_check = self.get_widget_by_name("cooldown_after_free")
            if cooldown_check and "noTurnCostCooldown" in data:
                cooldown_check.setChecked(data["noTurnCostCooldown"])
            
            # Stacks with other abilities
            stacks_check = self.get_widget_by_name("stacks")
            if stacks_check and "noTurnCostStacks" in data:
                stacks_check.setChecked(data["noTurnCostStacks"])
            
            # Only on successful activation
            success_check = self.get_widget_by_name("success_only")
            if success_check and "noTurnCostSuccessOnly" in data:
                success_check.setChecked(data["noTurnCostSuccessOnly"])
            
            self.log_debug("No turn cost data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the no turn cost configuration data
        """
        try:
            data = {}
            
            # Always no turn cost
            always_check = self.get_widget_by_name("always_no_cost")
            if always_check:
                data["noTurnCostAlways"] = always_check.isChecked()
            
            # Conditional no turn cost
            conditional_check = self.get_widget_by_name("conditional_no_cost")
            if conditional_check:
                data["noTurnCostConditional"] = conditional_check.isChecked()
            
            # Condition type
            condition_combo = self.get_widget_by_name("condition_type")
            if condition_combo:
                data["noTurnCostCondition"] = condition_combo.currentText()
            
            # Uses per turn
            uses_spin = self.get_widget_by_name("uses_per_turn")
            if uses_spin:
                data["noTurnCostUses"] = uses_spin.value()
            
            # Cooldown after free use
            cooldown_check = self.get_widget_by_name("cooldown_after_free")
            if cooldown_check:
                data["noTurnCostCooldown"] = cooldown_check.isChecked()
            
            # Stacks with other abilities
            stacks_check = self.get_widget_by_name("stacks")
            if stacks_check:
                data["noTurnCostStacks"] = stacks_check.isChecked()
            
            # Only on successful activation
            success_check = self.get_widget_by_name("success_only")
            if success_check:
                data["noTurnCostSuccessOnly"] = success_check.isChecked()
            
            self.log_debug(f"Collected no turn cost data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
