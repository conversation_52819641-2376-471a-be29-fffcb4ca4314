#!/usr/bin/env python3
"""
Comprehensive test suite for Enhanced Cache Management System
Tests cache limits, automatic cleanup, memory monitoring, and file invalidation
"""

import os
import sys
import json
import time
import tempfile
import unittest
import threading
from pathlib import Path
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enhanced_cache_manager import EnhancedCacheManager, CacheEntry, CacheIntegratedDataManager

class TestEnhancedCacheManager(unittest.TestCase):
    """Test suite for EnhancedCacheManager"""
    
    def setUp(self):
        """Set up test environment"""
        self.cache_manager = EnhancedCacheManager(
            max_cache_size_mb=1,  # Small size for testing
            max_entries=10,
            cleanup_interval_seconds=1,  # Fast cleanup for testing
            memory_warning_threshold=0.95,  # High threshold for testing
            enable_file_watching=True
        )
        
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        self.test_files = []
    
    def tearDown(self):
        """Clean up test environment"""
        self.cache_manager.shutdown()
        
        # Clean up test files
        for file_path in self.test_files:
            try:
                os.unlink(file_path)
            except:
                pass
        
        try:
            os.rmdir(self.temp_dir)
        except:
            pass
    
    def create_test_file(self, content: dict, filename: str = None) -> str:
        """Create a test file and return its path"""
        if filename is None:
            filename = f"test_{len(self.test_files)}.json"
        
        file_path = os.path.join(self.temp_dir, filename)
        with open(file_path, 'w') as f:
            json.dump(content, f)
        
        self.test_files.append(file_path)
        return file_path
    
    def test_basic_cache_operations(self):
        """Test basic cache get/set operations"""
        # Test piece caching
        test_data = {"name": "test_piece", "type": "pawn"}
        self.cache_manager.set_piece("test_key", test_data)
        
        retrieved = self.cache_manager.get_piece("test_key")
        self.assertEqual(retrieved, test_data)
        
        # Test ability caching
        test_ability = {"name": "test_ability", "range": 3}
        self.cache_manager.set_ability("ability_key", test_ability)
        
        retrieved_ability = self.cache_manager.get_ability("ability_key")
        self.assertEqual(retrieved_ability, test_ability)
        
        # Test cache miss
        missing = self.cache_manager.get_piece("nonexistent")
        self.assertIsNone(missing)
    
    def test_cache_statistics(self):
        """Test cache statistics tracking"""
        # Initial stats
        stats = self.cache_manager.get_cache_stats()
        initial_hits = stats['performance']['hits']
        initial_misses = stats['performance']['misses']
        
        # Cache miss
        self.cache_manager.get_piece("missing")
        
        # Cache hit
        self.cache_manager.set_piece("test", {"data": "value"})
        self.cache_manager.get_piece("test")
        
        # Check updated stats
        stats = self.cache_manager.get_cache_stats()
        self.assertEqual(stats['performance']['hits'], initial_hits + 1)
        self.assertEqual(stats['performance']['misses'], initial_misses + 1)
        self.assertEqual(stats['entries']['pieces'], 1)
    
    def test_lru_eviction(self):
        """Test LRU eviction when cache limits are exceeded"""
        # Fill cache beyond max entries
        for i in range(15):  # More than max_entries (10)
            self.cache_manager.set_piece(f"piece_{i}", {"id": i})
        
        stats = self.cache_manager.get_cache_stats()
        
        # Should have evicted some entries
        self.assertLessEqual(stats['entries']['total'], self.cache_manager.max_entries)
        self.assertGreater(stats['maintenance']['evictions'], 0)
        
        # Oldest entries should be evicted
        self.assertIsNone(self.cache_manager.get_piece("piece_0"))
        self.assertIsNotNone(self.cache_manager.get_piece("piece_14"))
    
    def test_file_invalidation(self):
        """Test cache invalidation when files are modified"""
        # Create test file
        test_data = {"name": "test", "value": "original"}
        file_path = self.create_test_file(test_data)
        
        # Cache the data
        self.cache_manager.set_piece("file_test", test_data, file_path)
        
        # Verify cached
        cached = self.cache_manager.get_piece("file_test")
        self.assertEqual(cached, test_data)
        
        # Modify the file
        time.sleep(0.1)  # Ensure different mtime
        modified_data = {"name": "test", "value": "modified"}
        with open(file_path, 'w') as f:
            json.dump(modified_data, f)
        
        # Cache should be invalidated
        cached_after_modify = self.cache_manager.get_piece("file_test")
        self.assertIsNone(cached_after_modify)
        
        # Stats should show invalidation
        stats = self.cache_manager.get_cache_stats()
        self.assertGreater(stats['maintenance']['invalidations'], 0)
    
    def test_manual_invalidation(self):
        """Test manual cache invalidation"""
        file_path = self.create_test_file({"test": "data"})
        
        self.cache_manager.set_piece("manual_test", {"data": "test"}, file_path)
        self.assertIsNotNone(self.cache_manager.get_piece("manual_test"))
        
        # Manual invalidation
        self.cache_manager.invalidate_file(file_path)
        self.assertIsNone(self.cache_manager.get_piece("manual_test"))
    
    def test_deleted_file_cleanup(self):
        """Test cleanup of cache entries for deleted files"""
        # Create and cache file
        file_path = self.create_test_file({"test": "data"})
        self.cache_manager.set_piece("delete_test", {"data": "test"}, file_path)
        
        # Verify cached
        self.assertIsNotNone(self.cache_manager.get_piece("delete_test"))
        
        # Delete file
        os.unlink(file_path)
        self.test_files.remove(file_path)
        
        # Trigger cleanup
        self.cache_manager._cleanup_deleted_files()
        
        # Cache entry should be removed
        self.assertIsNone(self.cache_manager.get_piece("delete_test"))
    
    @patch('enhanced_cache_manager.psutil.virtual_memory')
    def test_memory_monitoring(self, mock_memory):
        """Test memory usage monitoring and warnings"""
        # Create cache manager with lower threshold for testing
        test_cache = EnhancedCacheManager(memory_warning_threshold=0.7)  # 70% threshold

        # Mock high memory usage (above threshold)
        mock_memory.return_value = MagicMock(percent=85.0)

        # Reset last warning time to ensure warning triggers
        test_cache.last_memory_warning = datetime.min

        # This should trigger memory warning
        test_cache._check_memory_usage()

        stats = test_cache.get_cache_stats()
        self.assertGreater(stats['maintenance']['memory_warnings'], 0)

        # Clean up
        test_cache.shutdown()
    
    def test_cache_clear(self):
        """Test clearing all cache entries"""
        # Add some data
        self.cache_manager.set_piece("test1", {"data": 1})
        self.cache_manager.set_ability("test2", {"data": 2})
        
        # Verify data exists
        self.assertIsNotNone(self.cache_manager.get_piece("test1"))
        self.assertIsNotNone(self.cache_manager.get_ability("test2"))
        
        # Clear cache
        self.cache_manager.clear_all()
        
        # Verify data is gone
        self.assertIsNone(self.cache_manager.get_piece("test1"))
        self.assertIsNone(self.cache_manager.get_ability("test2"))
        
        stats = self.cache_manager.get_cache_stats()
        self.assertEqual(stats['entries']['total'], 0)

class TestCacheIntegratedDataManager(unittest.TestCase):
    """Test suite for CacheIntegratedDataManager - Basic functionality only"""

    def setUp(self):
        """Set up test environment"""
        self.data_manager = CacheIntegratedDataManager()

    def tearDown(self):
        """Clean up test environment"""
        self.data_manager.cache_manager.shutdown()

    def test_cache_manager_integration(self):
        """Test that data manager integrates with cache manager"""
        # Test that cache manager is accessible
        self.assertIsNotNone(self.data_manager.cache_manager)

        # Test cache stats access
        stats = self.data_manager.get_cache_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn('entries', stats)
        self.assertIn('performance', stats)

        # Test cache clearing
        self.data_manager.clear_cache()
        stats_after = self.data_manager.get_cache_stats()
        self.assertEqual(stats_after['entries']['total'], 0)

    def test_error_logging(self):
        """Test error logging functionality"""
        # Initially no errors
        errors = self.data_manager.get_error_log()
        self.assertIsInstance(errors, list)

        # Clear error log
        self.data_manager.clear_error_log()
        errors_after = self.data_manager.get_error_log()
        self.assertEqual(len(errors_after), 0)

def run_cache_performance_test():
    """Run performance test for cache system"""
    print("\n" + "="*60)
    print("ENHANCED CACHE MANAGER - PERFORMANCE TEST")
    print("="*60)
    
    cache_manager = EnhancedCacheManager(max_cache_size_mb=10, max_entries=1000)
    
    # Test data
    test_pieces = []
    for i in range(100):
        test_pieces.append({
            "name": f"Piece_{i}",
            "type": "pawn" if i % 2 == 0 else "knight",
            "abilities": [f"ability_{j}" for j in range(i % 5)],
            "stats": {"health": i, "attack": i * 2}
        })
    
    # Performance test: Cache writes
    start_time = time.time()
    for i, piece in enumerate(test_pieces):
        cache_manager.set_piece(f"piece_{i}", piece)
    write_time = time.time() - start_time
    
    # Performance test: Cache reads (hits)
    start_time = time.time()
    for i in range(100):
        cache_manager.get_piece(f"piece_{i}")
    read_time = time.time() - start_time
    
    # Performance test: Cache misses
    start_time = time.time()
    for i in range(100, 200):
        cache_manager.get_piece(f"piece_{i}")
    miss_time = time.time() - start_time
    
    # Get final stats
    stats = cache_manager.get_cache_stats()
    
    print(f"Cache Write Performance: {write_time:.4f}s for 100 entries ({write_time*10:.2f}ms per entry)")
    print(f"Cache Read Performance: {read_time:.4f}s for 100 hits ({read_time*10:.2f}ms per hit)")
    print(f"Cache Miss Performance: {miss_time:.4f}s for 100 misses ({miss_time*10:.2f}ms per miss)")
    print(f"\nCache Statistics:")
    print(f"  Total Entries: {stats['entries']['total']}")
    print(f"  Cache Size: {stats['size']['total_mb']:.2f} MB")
    print(f"  Hit Rate: {stats['performance']['hit_rate']:.2%}")
    print(f"  Total Requests: {stats['performance']['total_requests']}")
    
    cache_manager.shutdown()
    print("\n🎉 Performance test completed successfully!")

if __name__ == "__main__":
    # Run unit tests
    print("Running Enhanced Cache Manager Tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance test
    run_cache_performance_test()
