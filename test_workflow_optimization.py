"""
Test script for Workflow Optimization System

This script tests all components of the workflow optimization system:
- Undo/Redo functionality
- Keyboard shortcuts
- Template system
- Auto-save features
- Integration with editors
"""

import sys
import os
import json
import tempfile
import logging
from pathlib import Path
from unittest.mock import Mock, MagicMock

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLineEdit, QTextEdit
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest

from workflow_optimization import (
    UndoRedoManager, KeyboardShortcutManager, 
    AutoSaveManager, TemplateManager
)
from workflow_integration import WorkflowIntegrator

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockEditor(QMainWindow):
    """Mock editor for testing workflow optimization"""
    
    def __init__(self):
        super().__init__()
        self.data_type = "piece"
        self.current_data = {}
        self.current_filename = None
        self.unsaved_changes = False
        
        # Create basic UI
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        self.name_edit = QLineEdit()
        self.description_edit = QTextEdit()
        layout.addWidget(self.name_edit)
        layout.addWidget(self.description_edit)
        
        # Mock methods
        self.reset_form = Mock()
        self.save_data = Mock(return_value=True)
        self.load_data = Mock()
        self.refresh_data = Mock()
    
    def collect_data(self):
        """Collect current form data"""
        return {
            "name": self.name_edit.text(),
            "description": self.description_edit.toPlainText(),
            "version": "1.0.0"
        }
    
    def load_data_from_dict(self, data):
        """Load data into form"""
        self.name_edit.setText(data.get("name", ""))
        self.description_edit.setPlainText(data.get("description", ""))
        self.current_data = data.copy()
    
    def populate_data(self, data):
        """Alias for load_data_from_dict"""
        self.load_data_from_dict(data)
    
    def set_field_value(self, field_name, value):
        """Set a specific field value"""
        if field_name == "name":
            self.name_edit.setText(str(value))
        elif field_name == "description":
            self.description_edit.setPlainText(str(value))
    
    def mark_unsaved_changes(self):
        """Mark unsaved changes"""
        self.unsaved_changes = True

def test_undo_redo_manager():
    """Test undo/redo functionality"""
    print("\n=== Testing Undo/Redo Manager ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    editor = MockEditor()
    
    # Initialize undo/redo manager
    undo_manager = UndoRedoManager(editor)
    
    # Test initial state
    assert not undo_manager.can_undo(), "Should not be able to undo initially"
    assert not undo_manager.can_redo(), "Should not be able to redo initially"
    
    # Test field change
    old_name = "Old Name"
    new_name = "New Name"
    editor.name_edit.setText(old_name)
    
    undo_manager.push_field_change("name", old_name, new_name)
    
    assert undo_manager.can_undo(), "Should be able to undo after change"
    assert not undo_manager.can_redo(), "Should not be able to redo after change"
    
    # Test undo
    undo_manager.undo_stack.undo()
    assert editor.name_edit.text() == old_name, f"Undo failed: expected '{old_name}', got '{editor.name_edit.text()}'"
    
    # Test redo
    undo_manager.undo_stack.redo()
    assert editor.name_edit.text() == new_name, f"Redo failed: expected '{new_name}', got '{editor.name_edit.text()}'"
    
    print("✓ Undo/Redo Manager tests passed")

def test_template_manager():
    """Test template system"""
    print("\n=== Testing Template Manager ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    editor = MockEditor()
    
    # Create temporary directory for templates
    with tempfile.TemporaryDirectory() as temp_dir:
        template_manager = TemplateManager(editor)
        template_manager.templates_dir = Path(temp_dir)
        
        # Test default templates creation
        template_manager.load_templates()
        
        assert len(template_manager.piece_templates) > 0, "Should have default piece templates"
        assert len(template_manager.ability_templates) > 0, "Should have default ability templates"
        
        # Test template application
        template_name = list(template_manager.piece_templates.keys())[0]
        success = template_manager.apply_template(template_name)
        assert success, f"Failed to apply template: {template_name}"
        
        # Test saving as template
        editor.name_edit.setText("Test Piece")
        editor.description_edit.setPlainText("Test Description")
        
        success = template_manager.save_as_template("Test Template", "Test template description")
        assert success, "Failed to save template"
        
        # Verify template was saved
        templates = template_manager.get_templates()
        assert "Test Template" in templates, "Template was not saved"
        
        print("✓ Template Manager tests passed")

def test_auto_save_manager():
    """Test auto-save functionality"""
    print("\n=== Testing Auto-Save Manager ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    editor = MockEditor()
    
    # Set up editor with filename
    editor.current_filename = "test_piece.json"
    
    # Initialize auto-save with short interval for testing
    auto_save_manager = AutoSaveManager(editor, interval_seconds=1)
    
    # Test auto-save setup
    assert auto_save_manager.interval_seconds == 1, "Auto-save interval not set correctly"
    
    # Test starting/stopping
    auto_save_manager.start()
    assert auto_save_manager.timer.isActive(), "Auto-save timer should be active"
    
    auto_save_manager.stop()
    assert not auto_save_manager.timer.isActive(), "Auto-save timer should be stopped"
    
    print("✓ Auto-Save Manager tests passed")

def test_keyboard_shortcuts():
    """Test keyboard shortcut system"""
    print("\n=== Testing Keyboard Shortcuts ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    editor = MockEditor()
    
    # Initialize shortcut manager
    shortcut_manager = KeyboardShortcutManager(editor)
    
    # Test shortcut registration
    assert len(shortcut_manager.shortcuts) > 0, "Should have registered shortcuts"
    
    # Test shortcut handlers exist
    assert hasattr(shortcut_manager, 'new_item'), "Should have new_item handler"
    assert hasattr(shortcut_manager, 'save_item'), "Should have save_item handler"
    assert hasattr(shortcut_manager, 'undo'), "Should have undo handler"
    assert hasattr(shortcut_manager, 'redo'), "Should have redo handler"
    
    print("✓ Keyboard Shortcuts tests passed")

def test_workflow_integration():
    """Test complete workflow integration"""
    print("\n=== Testing Workflow Integration ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    editor = MockEditor()
    
    # Initialize workflow integrator
    integrator = WorkflowIntegrator(editor)
    
    # Test that all managers are created
    assert integrator.undo_manager is not None, "Undo manager should be created"
    assert integrator.shortcut_manager is not None, "Shortcut manager should be created"
    assert integrator.auto_save_manager is not None, "Auto-save manager should be created"
    assert integrator.template_manager is not None, "Template manager should be created"
    
    # Test that editor has workflow methods
    assert hasattr(editor, 'undo_manager'), "Editor should have undo_manager"
    assert hasattr(editor, 'template_manager'), "Editor should have template_manager"
    assert hasattr(editor, 'show_templates'), "Editor should have show_templates method"
    
    # Test workflow status
    status = integrator.get_workflow_status()
    assert 'undo_redo' in status, "Status should include undo_redo"
    assert 'shortcuts' in status, "Status should include shortcuts"
    assert 'auto_save' in status, "Status should include auto_save"
    assert 'templates' in status, "Status should include templates"
    
    # Test feature toggling
    integrator.enable_feature("auto_save", False)
    assert not integrator.auto_save_manager.timer.isActive(), "Auto-save should be disabled"
    
    integrator.enable_feature("auto_save", True)
    # Note: Timer might not start immediately without a filename
    
    print("✓ Workflow Integration tests passed")

def test_template_data_integrity():
    """Test template data integrity"""
    print("\n=== Testing Template Data Integrity ===")
    
    app = QApplication.instance() or QApplication(sys.argv)
    editor = MockEditor()
    
    template_manager = TemplateManager(editor)
    
    # Test piece templates
    for template_name, template_data in template_manager.piece_templates.items():
        assert 'version' in template_data, f"Piece template {template_name} missing version"
        assert 'name' in template_data, f"Piece template {template_name} missing name field"
        assert 'description' in template_data, f"Piece template {template_name} missing description"
        
        # Test movement data if present
        if 'movement' in template_data:
            movement = template_data['movement']
            assert isinstance(movement, dict), f"Movement in {template_name} should be dict"
    
    # Test ability templates
    for template_name, template_data in template_manager.ability_templates.items():
        assert 'version' in template_data, f"Ability template {template_name} missing version"
        assert 'name' in template_data, f"Ability template {template_name} missing name field"
        assert 'description' in template_data, f"Ability template {template_name} missing description"
        assert 'cost' in template_data, f"Ability template {template_name} missing cost"
        assert 'tags' in template_data, f"Ability template {template_name} missing tags"
        
        # Test tags are list
        assert isinstance(template_data['tags'], list), f"Tags in {template_name} should be list"
    
    print("✓ Template Data Integrity tests passed")

def run_all_tests():
    """Run all workflow optimization tests"""
    print("Starting Workflow Optimization Tests...")
    
    try:
        test_undo_redo_manager()
        test_template_manager()
        test_auto_save_manager()
        test_keyboard_shortcuts()
        test_workflow_integration()
        test_template_data_integrity()
        
        print("\n🎉 All Workflow Optimization Tests Passed! 🎉")
        return True
        
    except Exception as e:
        print(f"\n❌ Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Ensure QApplication exists
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    success = run_all_tests()
    
    if not success:
        sys.exit(1)
    
    print("\nWorkflow optimization system is ready for use!")
