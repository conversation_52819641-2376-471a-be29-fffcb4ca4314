"""
Adventure Chess Creator - Main Application Entry Point

This module provides the main application window and navigation between different editors.
The application uses a QStackedWidget to switch between:
- Welcome page
- Piece Editor
- Ability Editor

Architecture:
- MainWindow: Main application window with menu bar and navigation
- WelcomePage: Simple welcome screen
- Editor integration: Seamless switching between different editors

Author: Adventure Chess Team
Version: 1.1.0
"""

# Standard library imports
import sys
from typing import Optional

# PyQt6 imports
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QPushButton, QLabel, QStackedWidget,
    QFileDialog, QMessageBox, QMainWindow, QSizePolicy, QScrollArea
)
from PyQt6.QtGui import QAction, QKeySequence
from PyQt6.QtCore import Qt

# Local imports
from config import MAIN_WINDOW_DEFAULT, MAIN_WINDOW_MIN
from ui.ui_utils import setup_responsive_window, ResponsiveLayout, make_widget_responsive

# Workflow optimization imports
from workflow_integration import integrate_workflow_optimization, add_workflow_menu

# Visual feedback enhancement imports
from visual_feedback_integration import (
    integrate_visual_feedback_into_piece_editor,
    integrate_visual_feedback_into_ability_editor,
    get_visual_feedback_manager
)

# Editor imports - lazy loaded to improve startup time
from editors.piece_editor import PieceEditorWindow
from editors.ability_editor import AbilityEditorWindow

class WelcomePage(QWidget):
    """
    Welcome page widget providing a simple landing screen.

    This widget displays a welcome message for the Adventure Chess Creator.
    Navigation is handled by the main window's navigation buttons.
    """

    def __init__(self) -> None:
        """Initialize the welcome page with a centered welcome message."""
        super().__init__()
        layout = QVBoxLayout()

        # Welcome message with HTML formatting
        label = QLabel("<h1>Welcome to Adventure Chess!</h1><p>Select a mode to begin.</p>")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("QLabel { padding: 20px; }")

        layout.addWidget(label)
        self.setLayout(layout)

class MainWindow(QMainWindow):
    """
    Main application window for Adventure Chess Creator.

    This window provides navigation between different editors using a QStackedWidget
    architecture. It includes:
    - Menu bar with file operations
    - Navigation buttons for switching between editors
    - Responsive layout that adapts to different screen sizes

    Attributes:
        stack: QStackedWidget containing different editor pages
        editor_btn: Button to switch to piece editor
        ability_editor_btn: Button to switch to ability editor
        piece_editor: Lazy-loaded piece editor instance
        ability_editor: Lazy-loaded ability editor instance
    """

    def __init__(self) -> None:
        """
        Initialize the main application window.

        Sets up the responsive layout, menu bar, navigation buttons,
        and stacked widget for editor switching.
        """
        super().__init__()
        self.setWindowTitle("Adventure Chess")

        # Setup responsive window with default and minimum sizes
        setup_responsive_window(self, MAIN_WINDOW_DEFAULT, MAIN_WINDOW_MIN)

        # Create menu bar (simplified)
        self.create_menus()

        # Main content layout
        content_widget = QWidget()
        content_layout = ResponsiveLayout.create_vbox(margin=0, spacing=2)
        
        # Navigation buttons (fixed at top)
        nav_layout = ResponsiveLayout.create_hbox(margin=2, spacing=3)
        self.editor_btn = QPushButton("Piece Editor")
        self.ability_editor_btn = QPushButton("Ability Editor")

        # Make navigation buttons responsive
        for btn in [self.editor_btn, self.ability_editor_btn]:
            btn.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            btn.setMinimumHeight(35)

        nav_layout.addWidget(self.editor_btn)
        nav_layout.addWidget(self.ability_editor_btn)
        content_layout.addLayout(nav_layout)
        
        # Stacked widget for different modes
        self.stack = QStackedWidget()
        make_widget_responsive(self.stack)
        
        self.welcome = WelcomePage()
        self.editor = PieceEditorWindow()
        self.ability_editor = AbilityEditorWindow()

        # Integrate workflow optimization into editors
        self.piece_workflow_integrator = integrate_workflow_optimization(self.editor)
        self.ability_workflow_integrator = integrate_workflow_optimization(self.ability_editor)

        # Add workflow menus to editors
        if self.piece_workflow_integrator:
            add_workflow_menu(self.editor, self.piece_workflow_integrator)
        if self.ability_workflow_integrator:
            add_workflow_menu(self.ability_editor, self.ability_workflow_integrator)

        # Integrate visual feedback enhancements into editors
        self.piece_visual_feedback = integrate_visual_feedback_into_piece_editor(self.editor)
        self.ability_visual_feedback = integrate_visual_feedback_into_ability_editor(self.ability_editor)
        self.visual_feedback_manager = get_visual_feedback_manager()

        self.stack.addWidget(self.welcome)
        self.stack.addWidget(self.editor)
        self.stack.addWidget(self.ability_editor)
        content_layout.addWidget(self.stack)
        
        content_widget.setLayout(content_layout)

        # Set as central widget (menu bar will stay fixed)
        self.setCentralWidget(content_widget)

        # Connect menu actions after widgets are created
        self.connect_menu_actions()

        self.editor_btn.clicked.connect(self.show_editor)
        self.ability_editor_btn.clicked.connect(self.show_ability_editor)
        self.show_welcome()

    def show_welcome(self) -> None:
        """
        Switch to the welcome page.

        Updates the stacked widget to show the welcome page and
        adjusts menu visibility accordingly.
        """
        self.stack.setCurrentWidget(self.welcome)
        self.update_menus('welcome')
        self.update_zoom_status()

    def show_editor(self) -> None:
        """
        Switch to the piece editor.

        Updates the stacked widget to show the piece editor and
        adjusts menu visibility accordingly.
        """
        self.stack.setCurrentWidget(self.editor)
        self.update_menus('editor')
        self.update_zoom_status()

    def show_ability_editor(self) -> None:
        """
        Switch to the ability editor.

        Updates the stacked widget to show the ability editor and
        adjusts menu visibility accordingly.
        """
        self.stack.setCurrentWidget(self.ability_editor)
        self.update_menus('ability_editor')
        self.update_zoom_status()

    def update_menus(self, mode: str) -> None:
        """
        Update menu visibility based on current mode.

        Args:
            mode: Current application mode ('welcome', 'editor', 'ability_editor')
        """
        # Hide/show menu actions based on mode
        if mode == 'welcome':
            self.file_menu.menuAction().setVisible(True)
            self.edit_menu.menuAction().setVisible(False)
            self.view_menu.menuAction().setVisible(False)
            self.help_menu.menuAction().setVisible(True)
            for act in [self.new_action, self.open_action, self.refresh_action, self.batch_update_action, self.exit_action]:
                act.setVisible(True)
            for act in [self.save_action, self.save_as_action, self.delete_action, self.export_action]:
                act.setVisible(False)
        elif mode == 'editor':
            self.file_menu.menuAction().setVisible(True)
            self.edit_menu.menuAction().setVisible(True)
            self.view_menu.menuAction().setVisible(True)
            self.help_menu.menuAction().setVisible(True)
            for act in [self.new_action, self.open_action, self.save_action, self.save_as_action, self.delete_action, self.export_action, self.refresh_action, self.batch_update_action, self.exit_action]:
                act.setVisible(True)

        elif mode == 'ability_editor':
            self.file_menu.menuAction().setVisible(True)
            self.edit_menu.menuAction().setVisible(True)
            self.view_menu.menuAction().setVisible(False)
            self.help_menu.menuAction().setVisible(True)
            self.new_action.setVisible(True)
            self.open_action.setVisible(True)
            self.save_action.setVisible(True)
            self.save_as_action.setVisible(True)
            self.delete_action.setVisible(False)
            self.export_action.setVisible(False)
            self.refresh_action.setVisible(True)
            self.batch_update_action.setVisible(True)
            self.exit_action.setVisible(True)

    def save_piece_as(self):
        # Placeholder for Save As logic
        fname, _ = QFileDialog.getSaveFileName(self, "Save Piece As", "", "JSON Files (*.json)")
        if fname:
            self.editor.save_piece_as(fname)

    def export_piece(self):
        # Placeholder for Export logic
        QMessageBox.information(self, "Export", "Export feature coming soon.")

    def show_batch_update(self):
        """Show the batch update dialog"""
        from dialogs.batch_update_dialog import BatchUpdateDialog
        dialog = BatchUpdateDialog(self)
        dialog.exec()

    def show_about(self):
        about_text = """Adventure Chess
Version 1.0

A customizable chess variant editor.

🔍 Zoom Controls:
• Ctrl + Plus: Zoom In
• Ctrl + Minus: Zoom Out
• Ctrl + 0: Reset Zoom
• Ctrl + Mouse Wheel: Zoom In/Out

The interface automatically scales to fit your screen size."""
        QMessageBox.about(self, "About Adventure Chess", about_text)

    def show_user_guide(self):
        QMessageBox.information(self, "User Guide", "User guide coming soon.")

    def show_report_bug(self):
        QMessageBox.information(self, "Report Bug", "Report bug feature coming soon.")

    def show_version(self):
        QMessageBox.information(self, "Version Info", "Adventure Chess Version 1.0")
    
    def toggle_log_panel(self):
        """Toggle log panel in the current active widget"""
        current_widget = self.stack.currentWidget()
        if hasattr(current_widget, 'toggle_log_panel'):
            current_widget.toggle_log_panel()
        else:
            QMessageBox.information(self, "Toggle Log Panel", "Log panel toggle not available in current mode.")

    def zoom_in(self):
        """Zoom in the current active widget"""
        current_widget = self.stack.currentWidget()
        if hasattr(current_widget, 'zoom_in'):
            current_widget.zoom_in()
            self.update_zoom_status()
        else:
            # Try to find scroll areas in the widget
            scroll_areas = current_widget.findChildren(QScrollArea)
            for scroll_area in scroll_areas:
                if hasattr(scroll_area, 'zoom_in'):
                    scroll_area.zoom_in()
                    self.update_zoom_status()
                    break

    def zoom_out(self):
        """Zoom out the current active widget"""
        current_widget = self.stack.currentWidget()
        if hasattr(current_widget, 'zoom_out'):
            current_widget.zoom_out()
            self.update_zoom_status()
        else:
            # Try to find scroll areas in the widget
            scroll_areas = current_widget.findChildren(QScrollArea)
            for scroll_area in scroll_areas:
                if hasattr(scroll_area, 'zoom_out'):
                    scroll_area.zoom_out()
                    self.update_zoom_status()
                    break

    def reset_zoom(self):
        """Reset zoom for the current active widget"""
        current_widget = self.stack.currentWidget()
        if hasattr(current_widget, 'reset_zoom'):
            current_widget.reset_zoom()
            self.update_zoom_status()
        else:
            # Try to find scroll areas in the widget
            scroll_areas = current_widget.findChildren(QScrollArea)
            for scroll_area in scroll_areas:
                if hasattr(scroll_area, 'reset_zoom'):
                    scroll_area.reset_zoom()
                    self.update_zoom_status()
                    break

    def update_zoom_status(self):
        """Update zoom level in view menu"""
        current_widget = self.stack.currentWidget()
        zoom_factor = 1.0

        # Try to get zoom factor from scroll areas
        scroll_areas = current_widget.findChildren(QScrollArea)
        for scroll_area in scroll_areas:
            if hasattr(scroll_area, 'zoom_factor'):
                zoom_factor = scroll_area.zoom_factor
                break

        zoom_percent = int(zoom_factor * 100)
        self.zoom_level_action.setText(f"Current: {zoom_percent}%")

    def create_menus(self):
        """Create menu bar and menus"""
        # Menu Bar
        self.menu_bar = self.menuBar()
        self.file_menu = self.menu_bar.addMenu("File")
        self.edit_menu = self.menu_bar.addMenu("Edit")
        self.view_menu = self.menu_bar.addMenu("View")
        self.help_menu = self.menu_bar.addMenu("Help")

        # File menu actions
        self.new_action = QAction("New Piece", self)
        self.new_action.setShortcut(QKeySequence("Ctrl+N"))
        self.open_action = QAction("Open Piece", self)
        self.open_action.setShortcut(QKeySequence("Ctrl+O"))
        self.save_action = QAction("Save Piece", self)
        self.save_action.setShortcut(QKeySequence("Ctrl+S"))
        self.save_as_action = QAction("Save Piece As...", self)
        self.save_as_action.setShortcut(QKeySequence("Ctrl+Shift+S"))
        self.delete_action = QAction("Delete Piece", self)
        self.delete_action.setShortcut(QKeySequence("Delete"))
        self.export_action = QAction("Export Piece", self)

        # Batch update action
        self.batch_update_action = QAction("🔄 Batch Update All Files", self)
        self.batch_update_action.setToolTip("Update all ability and piece files to latest structure")

        # Refresh action
        self.refresh_action = QAction("🔄 Refresh Data", self)
        self.refresh_action.setShortcut(QKeySequence("F5"))
        self.refresh_action.setToolTip("Refresh all data and UI components")

        self.exit_action = QAction("Exit", self)
        self.exit_action.setShortcut(QKeySequence("Ctrl+Q"))

        self.file_menu.addActions([self.new_action, self.open_action, self.save_action, self.save_as_action, self.delete_action, self.export_action])
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.refresh_action)
        self.file_menu.addAction(self.batch_update_action)
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.exit_action)

        # Edit menu actions
        self.undo_action = QAction("Undo", self)
        self.undo_action.setShortcut(QKeySequence("Ctrl+Z"))
        self.redo_action = QAction("Redo", self)
        self.redo_action.setShortcut(QKeySequence("Ctrl+Y"))
        self.cut_action = QAction("Cut", self)
        self.cut_action.setShortcut(QKeySequence("Ctrl+X"))
        self.copy_action = QAction("Copy", self)
        self.copy_action.setShortcut(QKeySequence("Ctrl+C"))
        self.paste_action = QAction("Paste", self)
        self.paste_action.setShortcut(QKeySequence("Ctrl+V"))
        self.duplicate_action = QAction("Duplicate Piece", self)
        self.prefs_action = QAction("Preferences", self)
        self.edit_menu.addActions([self.undo_action, self.redo_action, self.cut_action, self.copy_action, self.paste_action, self.duplicate_action, self.prefs_action])

        # View menu actions
        self.toggle_log_action = QAction("Toggle Log Panel", self)

        # Create zoom submenu
        self.zoom_menu = self.view_menu.addMenu("Zoom")

        # Zoom level display action (non-clickable)
        self.zoom_level_action = QAction("Current: 100%", self)
        self.zoom_level_action.setEnabled(False)  # Make it non-clickable

        self.zoom_in_action = QAction("Zoom In", self)
        self.zoom_in_action.setShortcut(QKeySequence("Ctrl++"))
        self.zoom_in_action.setToolTip("Zoom in to make content larger. You can also use Ctrl+Mouse Wheel.")

        self.zoom_out_action = QAction("Zoom Out", self)
        self.zoom_out_action.setShortcut(QKeySequence("Ctrl+-"))
        self.zoom_out_action.setToolTip("Zoom out to make content smaller. You can also use Ctrl+Mouse Wheel.")

        self.reset_layout_action = QAction("Reset Zoom", self)
        self.reset_layout_action.setShortcut(QKeySequence("Ctrl+0"))
        self.reset_layout_action.setToolTip("Reset zoom to 100% (normal size)")

        # Add actions to zoom submenu
        self.zoom_menu.addAction(self.zoom_level_action)
        self.zoom_menu.addSeparator()
        self.zoom_menu.addAction(self.zoom_in_action)
        self.zoom_menu.addAction(self.zoom_out_action)
        self.zoom_menu.addAction(self.reset_layout_action)

        self.theme_action = QAction("Switch Theme", self)

        # Add main actions to view menu
        self.view_menu.addAction(self.toggle_log_action)
        self.view_menu.addSeparator()
        self.view_menu.addAction(self.theme_action)

        # Help menu actions
        self.about_action = QAction("About", self)
        self.user_guide_action = QAction("User Guide", self)
        self.report_bug_action = QAction("Report Bug", self)
        self.version_action = QAction("Version Info", self)
        self.help_menu.addActions([self.about_action, self.user_guide_action, self.report_bug_action, self.version_action])

    def connect_menu_actions(self):
        """Connect menu actions to their handlers"""
        self.new_action.triggered.connect(self.editor.reset_form)
        self.open_action.triggered.connect(self.editor.open_piece_dialog)
        self.save_action.triggered.connect(self.editor.save_piece)
        self.save_as_action.triggered.connect(self.save_piece_as)
        self.delete_action.triggered.connect(self.editor.delete_piece if hasattr(self.editor, 'delete_piece') else lambda: None)
        self.export_action.triggered.connect(self.export_piece)
        self.refresh_action.triggered.connect(self.refresh_editor_data)
        self.batch_update_action.triggered.connect(self.show_batch_update)
        self.exit_action.triggered.connect(self.close)
        # Workflow optimization will replace these connections
        self.undo_action.triggered.connect(lambda: None)
        self.redo_action.triggered.connect(lambda: None)
        self.cut_action.triggered.connect(lambda: None)
        self.copy_action.triggered.connect(lambda: None)
        self.paste_action.triggered.connect(lambda: None)
        self.duplicate_action.triggered.connect(lambda: None)
        self.prefs_action.triggered.connect(lambda: None)
        self.toggle_log_action.triggered.connect(self.toggle_log_panel)
        self.zoom_in_action.triggered.connect(self.zoom_in)
        self.zoom_out_action.triggered.connect(self.zoom_out)
        self.reset_layout_action.triggered.connect(self.reset_zoom)
        self.theme_action.triggered.connect(lambda: None)
        self.about_action.triggered.connect(self.show_about)
        self.user_guide_action.triggered.connect(self.show_user_guide)
        self.report_bug_action.triggered.connect(self.show_report_bug)
        self.version_action.triggered.connect(self.show_version)

    def refresh_editor_data(self):
        """Refresh data in the current editor"""
        if hasattr(self.editor, 'refresh_all_data'):
            self.editor.refresh_all_data()
        else:
            # Fallback for editors without refresh_all_data method
            if hasattr(self.editor, 'refresh_file_lists'):
                self.editor.refresh_file_lists()
            print("Editor data refreshed")

def main():
    # Initialize logging
    from utils.utils import setup_logging
    setup_logging("INFO")
    
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
