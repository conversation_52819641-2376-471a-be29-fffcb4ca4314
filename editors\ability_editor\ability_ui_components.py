"""
Ability Editor UI Components for Adventure Chess Creator

This module contains all UI creation and layout logic for the ability editor:
- Main UI initialization
- Tab creation (Basic, Tags, Configuration)
- Widget setup and styling
- Layout management

Extracted from ability_editor.py to improve maintainability and separate
UI concerns from business logic.
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel, QComboBox,
    QLineEdit, QTextEdit, QSpinBox, QCheckBox, QSizePolicy
)
from PyQt6.QtCore import Qt

# Local imports
from ui.ui_utils import ResponsiveLayout, ResponsiveScrollArea, TabWidgetResponsive, make_widget_responsive
from ui.ui_shared_components import FileOperationsWidget, ValidationStatusWidget

logger = logging.getLogger(__name__)


class AbilityUIComponents:
    """
    Handles all UI component creation and layout for the ability editor.
    
    This class separates UI creation from business logic, making the code
    more maintainable and easier to modify.
    """
    
    def __init__(self, editor_instance):
        """
        Initialize UI components handler.
        
        Args:
            editor_instance: The AbilityEditorWindow instance
        """
        self.editor = editor_instance
    
    def init_main_ui(self) -> QWidget:
        """
        Initialize the main user interface.
        
        Returns:
            The central widget for the main window
        """
        try:
            main_layout = ResponsiveLayout.create_vbox()
            
            # Top toolbar with file operations
            toolbar_layout = self._create_toolbar()
            main_layout.addLayout(toolbar_layout)
            
            # Main content in tabs
            self.editor.tab_widget = self._create_tab_widget()
            main_layout.addWidget(self.editor.tab_widget)
            
            # Status display
            self.editor.status_widget = ValidationStatusWidget()
            main_layout.addWidget(self.editor.status_widget)
            
            # Keep status_label for backward compatibility
            self.editor.status_label = self.editor.status_widget.status_label
            
            # Create central widget
            central_widget = QWidget()
            central_widget.setLayout(main_layout)
            
            logger.info("Main UI initialized successfully")
            return central_widget
            
        except Exception as e:
            logger.error(f"Error initializing main UI: {e}")
            raise
    
    def _create_toolbar(self) -> QHBoxLayout:
        """
        Create the top toolbar with file operations.
        
        Returns:
            The toolbar layout
        """
        toolbar_layout = ResponsiveLayout.create_hbox(margin=5, spacing=5)
        
        # Create shared file operations widget
        self.editor.file_ops_widget = FileOperationsWidget("full")
        
        # Connect signals to ability-specific methods
        self.editor.file_ops_widget.connect_signals(
            new_func=self.editor.new_ability,
            load_func=self.editor.load_ability,
            save_func=self.editor.save_ability,
            save_as_func=self.editor.save_as_ability,
            delete_func=self.editor.delete_current_ability
        )
        
        # Customize button text for ability-specific operations
        self.editor.file_ops_widget.new_btn.setText("📄 New Ability")
        self.editor.file_ops_widget.load_btn.setText("📂 Load Ability")
        self.editor.file_ops_widget.save_btn.setText("💾 Save Ability")
        self.editor.file_ops_widget.delete_btn.setText("🗑️ Delete Ability")
        self.editor.file_ops_widget.delete_btn.setStyleSheet("QPushButton { color: #d32f2f; }")
        
        # Load combo for ability search/selection
        self.editor.load_combo = QComboBox()
        self.editor.load_combo.setEditable(True)
        self.editor.load_combo.setPlaceholderText("Search abilities...")
        self.editor.load_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.editor.load_combo.setMinimumHeight(30)
        self.editor.load_combo.currentTextChanged.connect(self.editor.on_load_selection_changed)
        
        toolbar_layout.addWidget(self.editor.file_ops_widget)
        toolbar_layout.addWidget(QLabel("Load:"))
        toolbar_layout.addWidget(self.editor.load_combo)
        toolbar_layout.addStretch()
        
        return toolbar_layout
    
    def _create_tab_widget(self) -> QTabWidget:
        """
        Create the main tab widget with all tabs.
        
        Returns:
            The configured tab widget
        """
        tab_widget = QTabWidget()
        TabWidgetResponsive.setup_tab_widget(tab_widget)
        make_widget_responsive(tab_widget)
        
        # Basic Info Tab
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "Basic Info")
        
        # Tags Tab
        tags_tab = self.create_tags_tab()
        tab_widget.addTab(tags_tab, "Ability Tags")
        
        # Configuration Tab (dynamic based on selected tags)
        config_tab = self.create_config_tab()
        tab_widget.addTab(config_tab, "Configuration")
        
        return tab_widget
    
    def create_basic_tab(self) -> QWidget:
        """
        Create the basic info tab.
        
        Returns:
            The basic tab widget
        """
        try:
            # Create scrollable content
            scroll_area = ResponsiveScrollArea()
            
            # Basic fields layout
            form_layout = ResponsiveLayout.create_form_layout()
            
            # Name field
            self.editor.name_edit = QLineEdit()
            self.editor.name_edit.setPlaceholderText("Enter ability name...")
            self.editor.name_edit.textChanged.connect(self.editor.mark_unsaved_changes)
            form_layout.addRow("Name:", self.editor.name_edit)
            
            # Description field
            self.editor.description_edit = QTextEdit()
            self.editor.description_edit.setPlaceholderText("Enter ability description...")
            self.editor.description_edit.setMaximumHeight(100)
            self.editor.description_edit.textChanged.connect(self.editor.mark_unsaved_changes)
            form_layout.addRow("Description:", self.editor.description_edit)
            
            # Cost field
            cost_layout = ResponsiveLayout.create_hbox()
            self.editor.cost_spin = QSpinBox()
            self.editor.cost_spin.setRange(0, 999)
            self.editor.cost_spin.setValue(0)
            self.editor.cost_spin.valueChanged.connect(self.editor.mark_unsaved_changes)
            
            self.editor.auto_cost_check = QCheckBox("Auto-calculate cost")
            self.editor.auto_cost_check.stateChanged.connect(self.editor.mark_unsaved_changes)
            
            cost_layout.addWidget(self.editor.cost_spin)
            cost_layout.addWidget(self.editor.auto_cost_check)
            cost_layout.addStretch()
            form_layout.addRow("Cost:", cost_layout)
            
            # Activation mode
            self.editor.activation_combo = QComboBox()
            self.editor.activation_combo.addItems(["auto", "click"])
            self.editor.activation_combo.currentTextChanged.connect(self.editor.mark_unsaved_changes)
            form_layout.addRow("Activation Mode:", self.editor.activation_combo)
            
            # Create form widget and add to scroll area
            form_widget = QWidget()
            form_widget.setLayout(form_layout)
            scroll_area.add_widget(form_widget)
            
            logger.debug("Basic tab created successfully")
            return scroll_area
            
        except Exception as e:
            logger.error(f"Error creating basic tab: {e}")
            raise
    
    def create_tags_tab(self) -> QWidget:
        """
        Create the ability tags tab.
        
        Returns:
            The tags tab widget
        """
        try:
            # Create scrollable content
            scroll_area = ResponsiveScrollArea()
            
            # Add instructions at the top
            instructions = QLabel(
                "Select the tags that apply to this ability. "
                "The Configuration tab will update based on your selections."
            )
            instructions.setWordWrap(True)
            instructions.setStyleSheet("QLabel { color: #666; font-style: italic; margin-bottom: 10px; }")
            
            # Create layout for tags
            tags_layout = ResponsiveLayout.create_vbox()
            tags_layout.addWidget(instructions)
            
            # This will be populated by the tag manager
            self.editor.tags_layout = tags_layout
            
            # Create tags widget and add to scroll area
            tags_widget = QWidget()
            tags_widget.setLayout(tags_layout)
            scroll_area.add_widget(tags_widget)
            
            logger.debug("Tags tab created successfully")
            return scroll_area
            
        except Exception as e:
            logger.error(f"Error creating tags tab: {e}")
            raise
    
    def create_config_tab(self) -> QWidget:
        """
        Create the configuration tab.
        
        Returns:
            The configuration tab widget
        """
        try:
            # Use responsive scroll area directly
            self.editor.config_scroll_area = ResponsiveScrollArea()
            
            # Store reference to the content layout for dynamic updates
            self.editor.config_content_layout = ResponsiveLayout.create_vbox()
            
            # Initial message
            initial_label = QLabel("Select ability tags to see configuration options.")
            initial_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            initial_label.setStyleSheet("QLabel { color: #888; font-style: italic; }")
            self.editor.config_content_layout.addWidget(initial_label)
            
            # Add stretch to push content to top
            self.editor.config_content_layout.addStretch()
            
            # Create config widget and add to scroll area
            config_widget = QWidget()
            config_widget.setLayout(self.editor.config_content_layout)
            self.editor.config_scroll_area.add_widget(config_widget)
            
            logger.debug("Configuration tab created successfully")
            return self.editor.config_scroll_area
            
        except Exception as e:
            logger.error(f"Error creating configuration tab: {e}")
            raise
    
    def setup_change_tracking(self) -> None:
        """Setup automatic change tracking for UI widgets."""
        try:
            # This method can be called to connect change signals
            # to the editor's mark_unsaved_changes method
            
            # Basic tab widgets are already connected in create_basic_tab
            # Additional widgets can be connected here as needed
            
            logger.debug("Change tracking setup completed")
            
        except Exception as e:
            logger.error(f"Error setting up change tracking: {e}")
    
    def refresh_ui_state(self) -> None:
        """Refresh the UI state after data changes."""
        try:
            # This method can be used to update UI state
            # after loading or saving data
            
            # Update combo box if needed
            if hasattr(self.editor, 'refresh_ability_list'):
                self.editor.refresh_ability_list()
            
            logger.debug("UI state refreshed")
            
        except Exception as e:
            logger.error(f"Error refreshing UI state: {e}")
