"""
Buff Piece tag configuration for the Adventure Chess Creator ability editor.
Handles temporary enhancement of target pieces with stat boosts or abilities.
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QVBoxLayout, QFormLayout, QGroupBox, QLabel, QSpinBox, QCheckBox
)
from PyQt6.QtCore import Qt

from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector


class BuffPieceConfig(BaseTagConfig):
    """Configuration for buffPiece tag - temporary piece enhancement."""
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "buffPiece")
    
    def get_title(self) -> str:
        return "⬆️ Buff Piece Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """Create the buff piece configuration UI."""
        try:
            # Main group box
            group = QGroupBox(self.get_title())
            layout = QVBoxLayout()
            
            # Description
            description = QLabel("Enhance target pieces with temporary abilities or stat boosts.")
            description.setWordWrap(True)
            layout.addWidget(description)
            
            # Target piece selector
            target_selector = InlinePieceSelector(self.editor, "Target Pieces", allow_costs=True)
            self.store_widget("buff_target_selector", target_selector)
            layout.addWidget(target_selector)
            
            # Duration settings
            duration_layout = QFormLayout()
            
            duration_spin = QSpinBox()
            duration_spin.setRange(0, 20)
            duration_spin.setValue(1)  # Default per glossary V1.0.1
            duration_spin.setSuffix(" turns")
            duration_spin.setSpecialValueText("Indefinite")
            duration_spin.setToolTip("Duration of the buff effect (0 = indefinite)")
            self.store_widget("buff_duration_spin", duration_spin)
            duration_layout.addRow("Duration:", duration_spin)
            
            layout.addLayout(duration_layout)
            
            # Buff options
            options_group = QGroupBox("Buff Options")
            options_layout = QVBoxLayout()
            
            # Movement boost
            movement_check = QCheckBox("Increase Movement Range")
            movement_check.setToolTip("Temporarily increases piece movement range")
            self.store_widget("buff_movement_check", movement_check)
            options_layout.addWidget(movement_check)
            
            # Attack boost
            attack_check = QCheckBox("Increase Attack Power")
            attack_check.setToolTip("Temporarily increases piece attack capabilities")
            self.store_widget("buff_attack_check", attack_check)
            options_layout.addWidget(attack_check)
            
            # Defense boost
            defense_check = QCheckBox("Increase Defense")
            defense_check.setToolTip("Temporarily increases piece defensive capabilities")
            self.store_widget("buff_defense_check", defense_check)
            options_layout.addWidget(defense_check)
            
            # Ability enhancement
            ability_check = QCheckBox("Grant Additional Abilities")
            ability_check.setToolTip("Temporarily grants new abilities to the piece")
            self.store_widget("buff_ability_check", ability_check)
            options_layout.addWidget(ability_check)
            
            options_group.setLayout(options_layout)
            layout.addWidget(options_group)
            
            group.setLayout(layout)
            parent_layout.addWidget(group)
            
            self.log_debug("Buff piece configuration UI created successfully")
            
        except Exception as e:
            self.log_error(f"Error creating buff piece UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI with data from an ability."""
        try:
            self.log_debug(f"Populating buff piece data: {data}")
            
            # Populate target selector
            target_selector = self.get_widget_by_name("buff_target_selector")
            if target_selector:
                buff_targets = data.get("buffTargets", data.get("buff_targets", []))
                if buff_targets:
                    target_selector.set_pieces(buff_targets)
            
            # Populate duration
            duration_spin = self.get_widget_by_name("buff_duration_spin")
            if duration_spin:
                duration = data.get("buffDuration", 1)
                duration_spin.setValue(duration)
            
            # Populate buff options
            buff_options = data.get("buffOptions", {})
            
            movement_check = self.get_widget_by_name("buff_movement_check")
            if movement_check:
                movement_check.setChecked(buff_options.get("movement", False))
            
            attack_check = self.get_widget_by_name("buff_attack_check")
            if attack_check:
                attack_check.setChecked(buff_options.get("attack", False))
            
            defense_check = self.get_widget_by_name("buff_defense_check")
            if defense_check:
                defense_check.setChecked(buff_options.get("defense", False))
            
            ability_check = self.get_widget_by_name("buff_ability_check")
            if ability_check:
                ability_check.setChecked(buff_options.get("abilities", False))
            
            self.log_debug("Buff piece data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating buff piece data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            data = {}
            
            # Collect target pieces
            target_selector = self.get_widget_by_name("buff_target_selector")
            if target_selector:
                pieces = target_selector.get_pieces()
                if pieces:
                    data["buffTargets"] = pieces
            
            # Collect duration
            duration_spin = self.get_widget_by_name("buff_duration_spin")
            if duration_spin:
                data["buffDuration"] = duration_spin.value()
            
            # Collect buff options
            buff_options = {}
            
            movement_check = self.get_widget_by_name("buff_movement_check")
            if movement_check and movement_check.isChecked():
                buff_options["movement"] = True
            
            attack_check = self.get_widget_by_name("buff_attack_check")
            if attack_check and attack_check.isChecked():
                buff_options["attack"] = True
            
            defense_check = self.get_widget_by_name("buff_defense_check")
            if defense_check and defense_check.isChecked():
                buff_options["defense"] = True
            
            ability_check = self.get_widget_by_name("buff_ability_check")
            if ability_check and ability_check.isChecked():
                buff_options["abilities"] = True
            
            if buff_options:
                data["buffOptions"] = buff_options
            
            self.log_debug(f"Collected buff piece data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting buff piece data: {e}")
            return {}
