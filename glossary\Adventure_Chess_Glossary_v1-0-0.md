# Adventure Chess Glossary v1.0.0

## Piece Editor

### Basic Information
- **Name**: The display name of the piece
- **Description**: A brief description of what the piece does
- **Cost**: The point cost to include this piece in an army
- **Promotion**: The piece this becomes when promoted (optional)
- **Secondary Promotion**: An alternative promotion option (optional)

### Movement & Attack Patterns
- **Movement Pattern**: Defines where the piece can move (blue squares)
- **Attack Pattern**: Defines where the piece can attack (red squares)
- **Combined Pattern**: Some pieces use purple squares for squares that allow both movement and attack

### Special Properties
- **Can Capture**: Whether the piece can capture enemy pieces
- **Can Be Captured**: Whether this piece can be captured by enemies
- **Can Promote**: Whether the piece can be promoted under certain conditions

## Ability Editor

### Basic Information
- **Name**: The display name of the ability
- **Description**: A brief description of what the ability does
- **Cost**: The point cost to use this ability
- **Activation Mode**: When/how the ability can be activated

### Ability Tags
Abilities are built using a combination of tags that define their behavior:

#### Action Tags
- **move**: Allows the piece to move to a target square
- **summon**: Creates new pieces on the board
- **revival**: Brings back destroyed pieces
- **capture**: Destroys target pieces
- **carryPiece**: Allows carrying other pieces
- **swapPlaces**: Exchanges positions with another piece

#### Targeting Tags
- **range**: Defines the range/area where the ability can be used
- **areaEffect**: Affects multiple squares around the target

#### Condition Tags
- **adjacencyRequired**: Only works when adjacent to specific pieces
- **losRequired**: Requires line of sight to the target
- **noTurnCost**: Doesn't consume turn points when used

#### Special Tags
- **shareSpace**: Allows multiple pieces on the same square
- **delay**: Ability effect happens after a delay
- **passThrough**: Can target through other pieces

### Configuration Options
Each tag provides specific configuration options when selected. For example:
- **Range**: Allows setting custom range patterns
- **Summon**: Allows selecting which pieces can be summoned
- **Revival**: Allows selecting which pieces can be revived

## File Management
- **Save**: Saves the current piece or ability to a file
- **Load**: Loads a previously saved piece or ability
- **New**: Creates a new piece or ability from scratch

## Preview System
Both editors provide real-time previews of how the piece or ability will function in the game.
