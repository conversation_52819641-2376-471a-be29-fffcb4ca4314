"""
Capture tag configuration for ability editor.
Handles capture-based ability configurations.
"""

from PyQt6.QtWidgets import QComboBox, QCheckBox, QWidget
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class CaptureConfig(BaseTagConfig):
    """Configuration for capture tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "capture")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for capture configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting capture UI creation")
            
            # Create form layout
            form_layout = self.create_form_layout()
            self.log_debug("Created form layout")
            
            # Capture target dropdown
            capture_target = QComboBox()
            capture_target.addItems([
                "Enemy",       # capture enemy pieces only
                "Friendly",    # capture friendly pieces only
                "Any",         # capture any pieces
                "Neutral"      # capture neutral pieces only
            ])
            capture_target.setToolTip("Type of pieces that can be captured")
            self.store_widget("capture_target", capture_target)
            self.connect_change_signals(capture_target)
            form_layout.addRow("Capture Target:", capture_target)
            self.log_debug("Added capture target dropdown")
            
            # Destroy on capture checkbox
            destroy_on_capture = QCheckBox("Destroy captured pieces")
            destroy_on_capture.setChecked(True)
            destroy_on_capture.setToolTip("Whether captured pieces are destroyed or just moved")
            self.store_widget("capture_destroy", destroy_on_capture)
            self.connect_change_signals(destroy_on_capture)
            form_layout.addRow("", destroy_on_capture)
            self.log_debug("Added destroy on capture checkbox")
            
            # Capture through pieces checkbox
            capture_through = QCheckBox("Can capture through pieces")
            capture_through.setToolTip("Whether the capture can go through other pieces")
            self.store_widget("capture_through_pieces", capture_through)
            self.connect_change_signals(capture_through)
            form_layout.addRow("", capture_through)
            self.log_debug("Added capture through pieces checkbox")
            
            # Must capture checkbox
            must_capture = QCheckBox("Must capture if possible")
            must_capture.setToolTip("Whether the ability must capture if a valid target exists")
            self.store_widget("capture_must_capture", must_capture)
            self.connect_change_signals(must_capture)
            form_layout.addRow("", must_capture)
            self.log_debug("Added must capture checkbox")
            
            # Chain capture checkbox
            chain_capture = QCheckBox("Allow chain captures")
            chain_capture.setToolTip("Whether multiple captures can be made in sequence")
            self.store_widget("capture_chain", chain_capture)
            self.connect_change_signals(chain_capture)
            form_layout.addRow("", chain_capture)
            self.log_debug("Added chain capture checkbox")
            
            # Add the form layout to the parent
            form_widget = QWidget()
            form_widget.setLayout(form_layout)
            parent_layout.addWidget(form_widget)
            
            self.log_debug("Capture UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting capture data population with: {data}")
            
            # Populate capture target
            capture_target = self.get_widget_by_name("capture_target")
            if capture_target:
                target_value = data.get("captureTarget", "Enemy")
                self.log_debug(f"Setting capture target to: {target_value}")
                index = capture_target.findText(target_value)
                if index >= 0:
                    capture_target.setCurrentIndex(index)
            
            # Populate destroy on capture
            destroy_on_capture = self.get_widget_by_name("capture_destroy")
            if destroy_on_capture:
                destroy_value = data.get("captureDestroy", True)
                self.log_debug(f"Setting destroy on capture to: {destroy_value}")
                destroy_on_capture.setChecked(destroy_value)
            
            # Populate capture through pieces
            capture_through = self.get_widget_by_name("capture_through_pieces")
            if capture_through:
                through_value = data.get("captureThroughPieces", False)
                self.log_debug(f"Setting capture through pieces to: {through_value}")
                capture_through.setChecked(through_value)
            
            # Populate must capture
            must_capture = self.get_widget_by_name("capture_must_capture")
            if must_capture:
                must_value = data.get("captureMustCapture", False)
                self.log_debug(f"Setting must capture to: {must_value}")
                must_capture.setChecked(must_value)
            
            # Populate chain capture
            chain_capture = self.get_widget_by_name("capture_chain")
            if chain_capture:
                chain_value = data.get("captureChain", False)
                self.log_debug(f"Setting chain capture to: {chain_value}")
                chain_capture.setChecked(chain_value)
            
            self.log_debug("Capture data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the capture configuration data
        """
        try:
            data = {}
            
            # Collect capture target
            capture_target = self.get_widget_by_name("capture_target")
            if capture_target:
                data["captureTarget"] = capture_target.currentText()
            
            # Collect destroy on capture
            destroy_on_capture = self.get_widget_by_name("capture_destroy")
            if destroy_on_capture:
                data["captureDestroy"] = destroy_on_capture.isChecked()
            
            # Collect capture through pieces
            capture_through = self.get_widget_by_name("capture_through_pieces")
            if capture_through:
                data["captureThroughPieces"] = capture_through.isChecked()
            
            # Collect must capture
            must_capture = self.get_widget_by_name("capture_must_capture")
            if must_capture:
                data["captureMustCapture"] = must_capture.isChecked()
            
            # Collect chain capture
            chain_capture = self.get_widget_by_name("capture_chain")
            if chain_capture:
                data["captureChain"] = chain_capture.isChecked()
            
            self.log_debug(f"Collected capture data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
