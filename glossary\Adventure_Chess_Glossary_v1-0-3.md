# Adventure Chess Glossary v1.0.3

!! This means a refactoring of both glossary and application is required !!

## Table of Contents
1. [Quick Reference Index](#quick-reference-index)
2. [Backend Developer Index](#backend-developer-index)
3. [Canonical Abilities Reference](#canonical-abilities-reference)
   - [Action Abilities (15)](#action-abilities-15)
   - [Targeting Abilities (2)](#targeting-abilities-2)
   - [Condition Abilities (3)](#condition-abilities-3)
   - [Special Abilities (8)](#special-abilities-8)
4. [Data Structure Reference](#data-structure-reference)
5. [Configuration Options Reference](#configuration-options-reference)
6. [Tooltip Reference Index](#tooltip-reference-index)
7. [Piece Editor Guide](#piece-editor-guide)
8. [Ability Editor Guide](#ability-editor-guide)
9. [Pattern & Range Editors](#pattern--range-editors)
10. [File Management](#file-management)
11. [Best Practices & Tips](#best-practices--tips)
12. [Troubleshooting](#troubleshooting)
13. [Version History](#version-history)

---

## Quick Reference Index

### Piece Editor Essentials
- **Name/Description**: Basic piece information
- **Role**: Commander (key piece) or Supporter (supports commander)
- **Icons**: Black and white piece icons with preview
- **Movement Types**: Orthogonal, Diagonal, Any, L-shape, Custom
- **Range Settings**: Distance values for each movement type
- **Capture Ability**: Yes/No radio buttons
- **Color Directional**: Piece behavior differs by color
- **Recharge System**: Points, starting points, recharge types
- **Abilities**: List of attached ability files
- **Promotions**: Primary and secondary promotion piece lists
- **Special Properties**: Can Castle, Track Starting Position

### Ability Editor Essentials
- **28 Canonical Abilities**: All verified and production-ready
- **Action Tags (15)**: move, summon, revival, capture, carryPiece, swapPlaces, displacePiece, immobilize, convertPiece, duplicate, buffPiece, debuffPiece, addObstacle, removeObstacle, trapTile
- **Targeting Tags (2)**: range, areaEffect
- **Condition Tags (3)**: adjacencyRequired, losRequired, noTurnCost
- **Special Tags (8)**: shareSpace, delay, passThrough, pulseEffect, fogOfWar, invisible, reaction, requiresStartingPosition
- **Quick Patterns**: ♜♝♛♞♚🌐 for instant range setting
- **User-Defined Costs**: Cost field is completely user-controlled

### Pattern Editors
- **Pattern Editor**: 6-color system (Empty→Move→Attack→Both→Action→Any) with clickable legend
- **Range Editor**: 2-color system (In Range/Out of Range) + quick patterns
- **Both Editors**: Starting square and continue off board checkboxes
- **Consolidated Layout**: Save/Cancel buttons on same row as checkboxes

---

## Backend Developer Index

### Core Systems
- **Canonical Abilities**: 28 verified abilities matching config.py ABILITY_TAGS
- **Tag Validation**: get_canonical_ability_tags() for validation
- **No Duplication**: All backend references use canonical sources
- **User-Controlled Costs**: No automatic cost calculation - users set their own costs

### Data Consistency
- **Single Source of Truth**: config.py ABILITY_TAGS defines all valid abilities
- **Unified Validation**: All tag validation uses centralized functions
- **Clean Architecture**: No deprecated abilities or duplicate configurations
- **Simple Cost Model**: Cost field is purely user-defined via spinner control

---

## Canonical Abilities Reference

### Action Abilities (15)

#### **move**
- **Function**: Teleports piece to target square within range
- **Data**: Uses range configuration only
- **UI**: Info label only (no additional configuration options)
- **Behavior**: Instant movement to valid target squares

#### **summon**
- **Function**: Creates new pieces at target locations
- **Data**: `summonList` (custom_widget), `summonMax` (spinner 1-10)
- **Behavior**: Spawns pieces from list up to max limit per use

#### **revival**
- **Function**: Resurrects destroyed pieces at target locations
- **Data**: `revivalList` (custom_widget), `revivalMax` (spinner 1-10)
- **Options**: `revivalSacrificeMode` (checkbox), `revivalMaxCost` (spinner), `revivalWithStartingPoints` (checkbox), `revivalWithinTurn` (spinner)
- **Behavior**: Brings back destroyed pieces with optional point costs

#### **capture**
- **Function**: Destroys pieces at target locations
- **Data**: `captureTarget` (dropdown: "Enemy"|"Friendly"|"Any")
- **Behavior**: Removes pieces from board instantly

#### **carryPiece** !! Both carry and drop range should have an option for custom that will load our range_editor_dialog !!
- **Function**: Allows piece to carry other pieces while moving
- **Data**: `carryList` (custom_widget), `carryRange` (spinner 0-8)
- **Options**: `carryDropOnDeath` (checkbox)
  - `carryDropMode` (dropdown: "random"|"selectable")
  - `carryDropRange` (spinner)
  - `carryDropCanCapture` (checkbox)
- **Additional**: `carryShareAbilities` (checkbox), `carryStartingPiece` (dropdown)
- **Behavior**: Carried pieces move with carrier and count as single unit

#### **swapPlaces**
- **Function**: Exchanges positions with target piece
- **Data**: `swapList` (custom_widget)
- **Behavior**: Instant position exchange within range

#### **displacePiece** !! verify it has custom displacement map option and that it uses our range_editor_dialog !!
- **Function**: Pushes target piece in specified direction
- **Data**: `direction` (dropdown: N/S/E/W/NE/NW/SE/SW), `distance` (spinner 1-8), `displaceTargetList` (custom_widget)
- **Behavior**: Moves target piece away from current position

#### **immobilize**
- **Function**: Prevents piece movement for specified turns
- **Data**: `duration` (spinner 1-10), `immobilizeTargetList` (custom_widget)
- **Behavior**: Temporarily disables target piece movement

#### **convertPiece**
- **Function**: Changes target pieces to different side/color
- **Data**: `convertTargetList` (custom_widget)
- **Behavior**: Transforms enemy pieces to friendly (or vice versa)

#### **duplicate** !! Use our range_editor_dialog instead of custom_widget for duplicate positions !!
- **Function**: Creates copies of piece at offset positions
- **Data**: `locationOffset` (custom_widget), `limit` (spinner 1-8)
- **Behavior**: Spawns identical copies at relative positions

#### **buffPiece**
- **Function**: Temporarily enhances target pieces
- **Data**: `buffTargetList` (custom_widget), `buffDuration` (spinner 1-10), `buffAbilities` (custom_widget), `buffMovementAttackPattern` (pattern_editor)
- **Behavior**: Adds temporary abilities or modifies movement/attack patterns

#### **debuffPiece**
- **Function**: Temporarily weakens target pieces
- **Data**: `debuffTargetList` (custom_widget), `debuffDuration` (spinner 1-10), `debuffPreventAbilities` (custom_widget)
- **Options**: `debuffPreventLoS` (checkbox), `debuffMovementAttackPattern` (pattern_editor)
- **Behavior**: Removes abilities or restricts movement/attack patterns

#### **addObstacle**
- **Function**: Places obstacles on target squares
- **Data**: `type` (dropdown: wall/spike/crystal/ice/fire/portal)
- **Behavior**: Creates terrain that blocks movement or has special effects

#### **removeObstacle**
- **Function**: Removes obstacles from target squares
- **Data**: `type` (dropdown: wall/spike/crystal/ice/fire/portal/any)
- **Behavior**: Clears specified obstacle types from board

#### **trapTile** !! Teleport uses an edit teleport range button not a spinner, and i want to make sure it is using range_editor !!
- **Function**: Creates hidden traps on target squares
- **Data**: `trapEffects` (custom_widget)
- **Options**: `capture` (checkbox), `immobilizeDuration` (spinner), `teleportRange` (spinner), `addAbility` (dropdown)
- **Behavior**: Hidden effects that trigger when pieces enter

### Targeting Abilities (2)

#### **range**
- **Function**: Defines targeting area for abilities
- **Data**: `rangeMask` (range_editor), `piecePosition` (range_editor)
- **Options**: `rangeFriendlyOnly` (checkbox), `rangeEnemyOnly` (checkbox)
- **Behavior**: Restricts ability usage to defined pattern

#### **areaEffect**
- **Function**: Affects multiple squares around target
- **Data**: `areaShape` (dropdown: Circle/Square/Cross/Line/Custom), `areaRadius` (spinner 1-8)
- **Options**: `areaEffectTarget` (custom_widget), `areaEffectCenter` (custom_widget)
  - `customAreaPattern` (pattern_editor) *when areaShape=Custom
- **Behavior**: Applies ability effect to area around target

### Condition Abilities (3)

#### **adjacencyRequired**
- **Function**: Ability only works when adjacent to specific pieces
- **Data**: `adjacencyList` (custom_widget), `adjacencyMaxDistance` (spinner 0-5)
- **Behavior**: Validates required pieces within distance before activation

#### **losRequired**
- **Function**: Requires clear line of sight to target
- **Options**: `losIgnoreEnemy` (checkbox), `losIgnoreAll` (checkbox)
- **Behavior**: Validates clear path before ability activation

#### **noTurnCost**
- **Function**: Ability doesn't consume turn points
- **Data**: `noTurnCostLimit` (spinner: 0=unlimited, 1-10=limited uses per turn)
- **Behavior**: Allows multiple uses without ending turn

### Special Abilities (8)

#### **shareSpace**
- **Function**: Multiple pieces can occupy same square
- **Data**: `shareSpaceMax` (spinner 2-8), `shareSpaceSameType` (checkbox)
- **Behavior**: Overrides normal piece collision rules

#### **delay**
- **Function**: Ability effect occurs after specified turns
- **Data**: *`delayTurns` (spinner 1-10) OR *`delayActions` (spinner 1-10), `delayCancelable` (checkbox)
- **Behavior**: Queues effect for future execution

#### **passThrough** !! Needs a range_editor added to data collected !!
- **Function**: Can target through other pieces
- **Data**: `passThroughList` (custom_widget), `passThroughCapture` (dropdown: None/Enemy/Friendly/Any)
- **Behavior**: Ignores specified pieces for targeting

#### **pulseEffect**
- **Function**: Repeating effect that triggers every N turns
- **Data**: `interval` (spinner 1-10)
- **Behavior**: Automatically re-triggers ability at set intervals

#### **fogOfWar**
- **Function**: Reveals hidden areas of the board
- **Data**: `visionType` (dropdown: sight/lantern), `fogRadius` (spinner 1-8), `fogDuration` (spinner 1-10), `fogCost` (spinner 0+)
- **Options**: `fogCustomRangePattern` (range_editor) *when visionType=sight
- **Behavior**: Temporarily reveals hidden board areas

#### **invisible**
- **Function**: Makes piece undetectable under certain conditions
- **Data**: `invisibilitySettings` (custom_widget)
- **Options**: `revealOnMove` (spinner), `revealOnCapture` (spinner), `revealOnAction` (spinner), `revealOnEnemyLoS` (checkbox)
- **Behavior**: Hides piece until reveal conditions are met

#### **reaction** !! use a piece selector  instead of the dropdown, and then add checkboxs for Move, Capture, Death, Enter Range, Exit Range, Turn Start and Turn End.!!
- **Function**: Triggers automatically in response to events
- **Data**: `eventType` (dropdown: onEnemyMove/onAllyMove/onEnemyCapture/onAllyCapture/onEnemyDeath/onAllyDeath/onPieceEnterRange/onPieceLeaveRange/onTurnStart/onTurnEnd), `usesAction` (checkbox)
- **Behavior**: Passive ability that activates on specific game events

#### **requiresStartingPosition**
- **Function**: Ability only works if piece hasn't moved from starting position
- **Behavior**: Validates piece is still at original spawn location

---

## Data Structure Reference

### Piece Object Structure
```json
{
  "version": "1.0.0",
  "name": "string",
  "description": "string",
  "role": "Commander|Supporter",
  "canCastle": "boolean",
  "trackStartingPosition": "boolean",
  "blackIcon": "string (filename)",
  "whiteIcon": "string (filename)",
  "movement": {
    "type": "orthogonal|diagonal|any|lShape|custom",
    "distance": "integer (1-8 for standard types)",
    "pattern": "8x8 integer array (for custom)",
    "piece_position": "[row, col] array (for custom)"
  },
  "canCapture": "boolean",
  "colorDirectional": "boolean",
  "maxPoints": "integer (0-99)",
  "startingPoints": "integer (0-99)",
  "rechargeType": "turnRecharge|adjacencyRecharge|committedRecharge",
  "turnPoints": "integer (points per turn)",
  "abilities": "array of ability filenames",
  "promotions": "array of piece names",
  "promotions_2nd": "array of piece names"
}
```

### Ability Object Structure
```json
{
  "version": "1.0.0",
  "name": "string",
  "description": "string",
  "cost": "integer (user-defined)",
  "activationMode": "auto|click",
  "tags": "array of canonical ability tags",
  "rangeMask": "8x8 boolean array",
  "piecePosition": "[row, col] array",
  "summonList": "array of piece objects",
  "revivalList": "array of piece objects",
  "carryList": "array of piece objects",
  "swapList": "array of piece objects",
  "adjacencyList": "array of piece objects",
  "passThroughList": "array of piece objects",
  "displaceTargetList": "array of piece objects",
  "immobilizeTargetList": "array of piece objects",
  "convertTargetList": "array of conversion rules",
  "buffTargetList": "array of piece objects",
  "debuffTargetList": "array of piece objects",
  "customAreaPattern": "8x8 boolean array",
  "buffMovementAttackPattern": "8x8 integer array",
  "debuffMovementAttackPattern": "8x8 integer array",
  "invisibilitySettings": "object with reveal conditions",
  "trapEffects": "object with trap effect definitions"
}
```

### Pattern Array Values
- **Movement Patterns (Piece Editor)**: 0=Empty, 1=Move, 2=Attack, 3=Both, 4=Action, 5=Any
- **Range Patterns (Ability Editor)**: true=In Range, false=Out of Range
- **Position Arrays**: [row, col] where row/col are 0-7 (top-left origin)
- **Custom Movement**: 8x8 integer array with piece_position for piece location

---

## Configuration Options Reference

### Range Configuration
- `rangeFriendlyOnly`: Ability only works on friendly pieces
- `rangeEnemyOnly`: Ability only works on enemy pieces
- Starting square inclusion handled in range editor dialog

### Area Effect Configuration
- `areaShape`: Circle, Square, Cross, Line, Custom
- `areaRadius`: Size of effect area (1-8)
- `areaEffectTarget`: [row, col] where ability targets
- `areaEffectCenter`: [row, col] center of effect area
  - `customAreaPattern`: Custom pattern (when areaShape=Custom)

### Summon/Revival Configuration !! Revival configuration seems to be missing the max pieces per turn spinner !!
- `summonMax`/`revivalMax`: Maximum pieces per use (1-10)
- `revivalSacrificeMode`: Use piece cost for revival when insufficient points
- `revivalMaxCost`: Maximum points usable during sacrifice !! Should be indented because it becomes available after clicking sacrifice mode checkbox !!
- `revivalWithStartingPoints`: Revive with starting turn points !! Should be indented and have the revive with checkbox as its condition for the spinner to be used , also needs information about the starting checkbox !!
- `revivalWithinTurn`: Must revive within N turns of death

### Carry Piece Configuration
- `carryRange`: Distance for picking up pieces (0-8)
- `carryDropOnDeath`: Drop carried pieces when carrier dies
  - `carryDropMode`: "random" or "selectable" (self removed from canonical)
  - `carryDropRange`: Distance for dropping pieces
  - `carryDropCanCapture`: Dropped pieces can capture
- `carryShareAbilities`: Carried pieces share abilities
- `carryStartingPiece`: Piece starts carrying specified piece

### Adjacency Configuration
- `adjacencyMaxDistance`: Maximum distance for adjacency (0-5)
- `adjacencyPieces`: Array of required adjacent pieces

### Line of Sight Configuration
- `losIgnoreEnemy`: Ignore enemy pieces for LoS calculation
- `losIgnoreAll`: Ignore all pieces for LoS calculation

### No Turn Cost Configuration
- `noTurnCostLimit`: Uses per turn (0=unlimited, 1-10=limited)

### Share Space Configuration
- `shareSpaceMax`: Maximum pieces per square (2-8)
- `shareSpaceSameType`: Only same piece types can share

### Delay Configuration
- *`delayTurns`: Number of turns to delay (1-10) OR
- *`delayActions`: Number of actions to delay (1-10)
- `delayCancelable`: Whether delay can be cancelled

### Pass Through Configuration
- `passThroughPieces`: Array of pieces that can be passed through
- `passThroughCapture`: None, Enemy, Friendly, Any

### Displacement Configuration
- `direction`: N, NE, E, SE, S, SW, W, NW
- `distance`: Distance to push piece (1-8)
- `displaceTargetList`: Array of valid displacement targets

### Immobilize Configuration
- `duration`: Turns to immobilize (1-10)
- `immobilizeTargetList`: Array of valid immobilize targets

### Pulse Effect Configuration
- `interval`: Turns between activations (1-10)

### Obstacle Configuration
- `type`: wall, spike, crystal, ice, fire, portal, any (for removal)

### Duplicate Configuration
- `locationOffset`: Array of [row, col] relative positions
- `limit`: Maximum duplicates (1-8)

### Reaction Configuration
- `eventType`: Trigger event type (see canonical list)
- `usesAction`: Whether reaction consumes action points

### Buff/Debuff Configuration
- `buffDuration`/`debuffDuration`: Effect duration (1-10)
- `buffMovementAttackPattern`/`debuffMovementAttackPattern`: Modified patterns
- `debuffPreventLoS`: Prevent line of sight abilities
- `buffAbilities`/`debuffPreventAbilities`: Ability modifications

### Invisibility Configuration
- `revealOnMove`: Turns visible after N moves
- `revealOnCapture`: Turns visible after N captures
- `revealOnAction`: Turns visible after N actions
- `revealOnEnemyLoS`: Visible to enemies with line of sight

### Trap Configuration
- `capture`: Trap destroys pieces
- `immobilizeDuration`: Turns to immobilize trapped pieces
- `teleportRange`: Distance for teleport traps
- `addAbility`: Ability name to grant to trapped pieces

### Fog of War Configuration
- `visionType`: sight, lantern
- `fogRadius`: Vision radius (1-8)
- `fogDuration`: Duration in turns (1-10, 0=permanent)
- `fogCost`: Point cost per use (0=no extra cost)
  - `fogCustomRangePattern`: Custom vision pattern (when visionType=sight)

---

## Tooltip Reference Index

### Piece Editor Tooltips
- **Role**: "Commander pieces are key pieces that must be protected. Supporter pieces assist commanders."
- **Can Castle**: "Allow this piece to participate in castling moves"
- **Track Starting Position**: "Remember where this piece started for abilities that require starting position"
- **Color Directional**: "Piece behavior changes based on which color is playing it"
- **Movement Type**: "How the piece moves: Orthogonal (+-shaped), Diagonal (X-shaped), Any (all directions), L-shape (knight-like), Custom (define your own)"
- **Recharge Types**: "turnRecharge: Gain points each turn | adjacencyRecharge: Gain points when near specific pieces | committedRecharge: Gain points when committing to actions" !! Committed recharge should display "Turns this piece cant move, take actions, or attack, after commitment you are fully recharged !!

### Ability Editor Tooltips
- **Activation Mode**: "auto: Ability triggers automatically when conditions are met | click: Player must click to activate"
- **Cost**: "Point cost for using this ability (set by user)"
- **Range Mask**: "8x8 grid defining where this ability can target"
- **Starting Square**: "Allow targeting the piece's starting position"
- **Continue Off Board**: "Allow targeting beyond board edges" !! should be continues board pattern off edges of map !!

### Pattern Editor Tooltips
- **Legend Colors**: "Click colors to enter paint mode, then click tiles to paint that color"
- **Clear Tile**: "Click to select clear mode, then click tiles to clear them"
- **Move Only**: "Click to select move mode, then click tiles to mark as move-only"
- **Attack Only**: "Click to select attack mode, then click tiles to mark as attack-only"
- **Move & Attack**: "Click to select both mode, then click tiles to mark as move and attack"
- **Action**: "Click to select action mode, then click tiles to mark as action"
- **Any**: "Click to select any mode, then click tiles to mark as any action"
- **Normal Mode**: "Return to normal cycling mode"

### Range Editor Tooltips
- **Quick Patterns**: "♜ Rook (orthogonal) | ♝ Bishop (diagonal) | ♛ Queen (any direction) | ♞ Knight (L-shape) | ♚ King (adjacent) | 🌐 Global (entire board)"
- **Max Distance**: "Maximum range for the selected pattern"

---

## Piece Editor Guide

### Basic Information
1. **Name & Description**: Enter piece name and description
2. **Role Selection**: Choose Commander (key piece) or Supporter
3. **Special Properties**:
   - Can Castle: Enable castling participation
   - Track Starting Position: Required for some abilities

### Icon Management
1. **Icon Selection**: Choose black and white piece icons
2. **Preview**: Icons show in preview area
3. **File Management**: Icons stored in icons/ folder

### Movement Configuration
1. **Movement Type**: Select from dropdown
   - Orthogonal: + shaped movement
   - Diagonal: X shaped movement
   - Any: All 8 directions
   - L-shape: Knight-like movement
   - Custom: Define your own pattern
2. **Distance**: Set range for standard movement types
3. **Custom Pattern**: Use pattern editor for custom movement

### Combat & Behavior
1. **Capture Ability**: Yes/No radio buttons
2. **Color Directional**: Different behavior per color

### Point System
1. **Max Points**: Maximum action points (0-99)
2. **Starting Points**: Points at game start (0-99)
3. **Recharge Type**: How points are gained
4. **Turn Points**: Points gained per turn

### Abilities & Promotions
1. **Abilities**: Attach ability files to piece
2. **Promotions**: Primary promotion options
3. **Secondary Promotions**: Alternative promotion options !! Secondary promotion Options !!

---

## Ability Editor Guide

### Basic Setup
1. **Name & Description**: Enter ability information
2. **Activation Mode**: Auto or Click activation
3. **Tags**: Select from 28 canonical ability tags
4. **Cost**: Set point cost for using this ability

### Tag Categories
1. **Action Tags (15)**: Core ability functions
2. **Targeting Tags (2)**: How ability targets
3. **Condition Tags (3)**: Requirements for activation
4. **Special Tags (8)**: Modifiers and special behaviors

### Configuration Sections
Each selected tag reveals relevant configuration options:
1. **Range Configuration**: Define targeting area
2. **Piece Lists**: Select valid targets/requirements
3. **Numeric Values**: Durations, distances, limits
4. **Special Options**: Tag-specific behaviors

### Validation & Preview
1. **Real-time Validation**: Errors shown immediately
2. **Cost Setting**: User sets cost via spinner control
3. **Preview**: Shows ability summary with current configuration

---

## Pattern & Range Editors

### Pattern Editor (6-Color System)
1. **Legend**: Click colors to enter paint mode
   - Clear (Empty): Remove targeting
   - Blue (Move Only): Movement targets
   - Red (Attack Only): Attack targets
   - Purple (Move & Attack): Both movement and attack
   - Yellow (Action): Special action targets
   - Green (Any): Any type of action
2. **Paint Mode**: Click legend color, then click tiles
3. **Normal Mode**: Click "Normal Mode" to return to cycling
4. **Quick Patterns**: Preset patterns for common shapes
5. **Options**: Starting square and continue off board checkboxes

### Range Editor (2-Color System)
1. **In Range/Out of Range**: Simple targeting definition
2. **Quick Patterns**: ♜♝♛♞♚🌐 for instant setup
3. **Max Distance**: Adjustable range for patterns
4. **Dynamic Visualization**: Grid expands with max distance

### Shared Features
1. **Consolidated Layout**: Checkboxes and buttons on same row
2. **Clear Button**: Reset all tiles to empty
3. **Save/Cancel**: Confirm or discard changes

---

## File Management

### File Structure
```
Adventure Chess/
├── pieces/          # Piece definition files (.json)
├── abilities/       # Ability definition files (.json)
├── icons/          # Piece icon files (.png, .jpg, etc.)
├── glossary/       # Documentation files
└── deprecated_ability_configs/  # Backup of removed features
```

### Saving & Loading
1. **Auto-save**: Changes saved automatically when switching tabs
2. **Manual Save**: Use Save buttons or Ctrl+S
3. **File Validation**: Automatic validation on load
4. **Error Handling**: Clear error messages for invalid files

### File Naming
1. **Pieces**: Descriptive names (e.g., "Knight.json", "Wizard.json")
2. **Abilities**: Action-based names (e.g., "Teleport.json", "Heal.json")
3. **Icons**: Match piece names when possible

### Backup & Recovery
1. **Deprecated Folder**: Removed features backed up automatically
2. **Version Control**: Consider using Git for change tracking
3. **Regular Backups**: Save copies of important configurations

---

## Best Practices & Tips

### Piece Design
1. **Balanced Costs**: Higher point costs for powerful abilities
2. **Clear Roles**: Distinguish commanders from supporters
3. **Thematic Consistency**: Abilities should match piece concept
4. **Testing**: Test piece behavior through external gameplay

### Ability Design
1. **Single Purpose**: Each ability should have one clear function
2. **Canonical Tags**: Only use the 28 verified canonical abilities
3. **Range Patterns**: Use quick patterns when possible for consistency
4. **Cost Balance**: Set appropriate costs based on ability power and game balance

### Pattern Creation
1. **Symmetry**: Consider symmetric patterns for balanced gameplay
2. **Range Limits**: Avoid overly large ranges that dominate the board
3. **Visual Clarity**: Use pattern editor colors effectively
4. **Testing**: Verify patterns work as expected in game

### Configuration Management
1. **Consistent Naming**: Use clear, descriptive names
2. **Documentation**: Add meaningful descriptions to all items
3. **Version Control**: Track changes to important configurations
4. **Validation**: Always check for errors before finalizing

---

## Troubleshooting

### Common Issues

#### **Pattern Editor Legend Not Clickable**
- **Fixed in v1.0.3**: Legend buttons now properly enabled
- **Solution**: Click legend colors to enter paint mode

#### **Carry Piece Drop Options Always Visible**
- **Fixed in v1.0.3**: Options now hidden when "Drop on Death" unchecked
- **Solution**: Check "Drop on Death" to see drop configuration options

#### **Move Ability Recursive Display**
- **Fixed in v1.0.3**: Removed phantom moveDelay data storage and display
- **Solution**: Move ability now shows only canonical configuration

#### **Non-Canonical Abilities in Editor**
- **Fixed in v1.0.3**: Removed preventAbility and other deprecated features
- **Solution**: Only 28 canonical abilities available

### File Issues
1. **Invalid JSON**: Check file syntax with JSON validator
2. **Missing Icons**: Ensure icon files exist in icons/ folder
3. **Circular References**: Avoid pieces that reference themselves
4. **Version Mismatch**: Update old files to current version format

### Performance Issues
1. **Large Patterns**: Simplify overly complex range patterns
2. **Too Many Abilities**: Limit abilities per piece for performance
3. **Icon Size**: Use appropriately sized icon files

### Validation Errors
1. **Required Fields**: Ensure all required fields are filled
2. **Range Validation**: Check numeric ranges are within limits
3. **Tag Conflicts**: Some ability tag combinations may conflict
4. **Pattern Validation**: Ensure patterns have valid piece positions

---

## Version History

### v1.0.3 (2025-06-21) - Canonical Abilities & Production Ready
**Major Changes:**
- ✅ **Canonical Compliance**: All 28 canonical abilities documented and verified
- ✅ **Backend Cleanup**: Removed deprecated abilities (preventAbility, revealTiles)
- ✅ **User-Controlled Costs**: Removed automatic cost calculation - users set their own costs
- ✅ **Pattern Editor Improvements**: Enhanced legend clicking and layout consolidation
- ✅ **Move Ability Fix**: Removed recursive display issue with phantom moveDelay data
- ✅ **Production Ready**: Clean foundation for game logic implementation

**Removed Features:**
- ❌ preventAbility tag and configuration (not in canonical abilities)
- ❌ revealTiles references (replaced by fogOfWar)
- ❌ teleport references (replaced by canonical move)
- ❌ Automatic cost calculation system (was unnecessary clutter)
- ❌ moveDelay phantom data storage and display

**Technical Improvements:**
- 🔧 Simplified cost model - purely user-defined
- 🔧 Unified tag validation system
- 🔧 Consolidated layout in pattern editors
- 🔧 Fixed legend button clicking in pattern editor
- 🔧 Improved carry piece drop options visibility
- 🔧 Fixed move ability to show only canonical configuration

### v1.0.2 (Previous) - Comprehensive Reference
- Multiple indexes for different user types
- Complete coverage of all ability tags and configuration options
- Technical details for backend developers
- Troubleshooting section

### v1.0.1 (Previous) - Enhanced Editors
- Updated for enhanced piece and ability editors
- New pattern and range editor documentation
- Improved configuration options

### v1.0.0 (Previous) - Initial Release
- Basic piece and ability editor documentation
- Core functionality coverage
- Initial structure and organization

---

## Comprehensive Potential Updates !! Incoroprate all updates in this section in next refactor !!

### UI Perspective Updates

#### **Pattern & Range Editors**
- **Pattern Preview**: Real-time preview of how patterns affect gameplay

#### **Ability Editor Enhancements**
- **Allow the ability to uncheck an ability from the configuration tab. add to the top left of the abilities display in the configuration tab.

#### **Piece Editor Improvements**
- **Icon Generator**: Built-in icon creation tools

#### **General UI Enhancements**
- **Dark/Light Theme Toggle**: User-selectable themes !! This is a big overhaul, save for after other updates !!
- **Keyboard Shortcuts**: Comprehensive keyboard navigation
- **Responsive Design**: Support for different screen sizes

### Backend Perspective Updates

#### **Data Management**
- **Data Validation**: Enhanced validation with detailed error reporting
- **Schema Evolution**: Automatic schema updates for new versions

#### **Widget optimization **
- **Piece selector and ability selector custom widgets**: Create Piece_selector_dialog.py and ability_selector_dialog.py and implement them into the ability editor. use current piece and ability selector customwidgets to create the .py's

---

**End of Glossary v1.0.3**

*This glossary reflects the current state of Adventure Chess editors as of 2025-06-21. All abilities and configurations have been verified against the canonical ABILITY_TAGS and are production-ready for game logic implementation.*
