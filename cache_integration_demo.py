#!/usr/bin/env python3
"""
Cache Integration Demonstration for Adventure Chess Creator
Shows how to integrate the enhanced cache management system with existing code
"""

import os
import sys
import json
import time
import logging
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enhanced_cache_manager import get_cache_manager, CacheIntegratedDataManager

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def demonstrate_cache_features():
    """Demonstrate the key features of the enhanced cache management system"""
    print("\n" + "="*70)
    print("ADVENTURE CHESS CREATOR - ENHANCED CACHE MANAGEMENT DEMO")
    print("="*70)
    
    # Get the global cache manager
    cache_manager = get_cache_manager()
    
    print("\n1. BASIC CACHE OPERATIONS")
    print("-" * 40)
    
    # Demonstrate basic caching
    test_piece = {
        "name": "Demo Knight",
        "type": "knight",
        "abilities": ["knight_move", "knight_jump"],
        "stats": {"health": 100, "attack": 50}
    }
    
    # Cache the piece
    cache_manager.set_piece("demo_knight", test_piece)
    print(f"✓ Cached piece: {test_piece['name']}")
    
    # Retrieve from cache
    cached_piece = cache_manager.get_piece("demo_knight")
    print(f"✓ Retrieved from cache: {cached_piece['name']}")
    
    # Test cache miss
    missing_piece = cache_manager.get_piece("nonexistent")
    print(f"✓ Cache miss handled: {missing_piece is None}")
    
    print("\n2. CACHE STATISTICS")
    print("-" * 40)
    
    stats = cache_manager.get_cache_stats()
    print(f"Cache Entries: {stats['entries']['total']}")
    print(f"Cache Size: {stats['size']['total_mb']:.3f} MB")
    print(f"Hit Rate: {stats['performance']['hit_rate']:.1%}")
    print(f"Total Requests: {stats['performance']['total_requests']}")
    
    print("\n3. FILE INVALIDATION DEMO")
    print("-" * 40)
    
    # Create a temporary file for demonstration
    temp_file = "temp_demo_piece.json"
    temp_path = Path(temp_file)
    
    try:
        # Create initial file
        initial_data = {"name": "Temp Piece", "version": 1}
        with open(temp_path, 'w') as f:
            json.dump(initial_data, f)
        
        # Cache with file path
        cache_manager.set_piece("temp_piece", initial_data, str(temp_path))
        print(f"✓ Cached piece with file path: {temp_file}")
        
        # Verify cached
        cached = cache_manager.get_piece("temp_piece")
        print(f"✓ Retrieved from cache: version {cached['version']}")
        
        # Modify the file
        time.sleep(0.1)  # Ensure different modification time
        modified_data = {"name": "Temp Piece", "version": 2}
        with open(temp_path, 'w') as f:
            json.dump(modified_data, f)
        
        # Cache should be invalidated
        cached_after_modify = cache_manager.get_piece("temp_piece")
        if cached_after_modify is None:
            print("✓ Cache automatically invalidated after file modification")
        else:
            print("✗ Cache invalidation failed")
        
    finally:
        # Clean up
        if temp_path.exists():
            temp_path.unlink()
    
    print("\n4. MEMORY MONITORING")
    print("-" * 40)
    
    # Force memory check
    cache_manager._check_memory_usage()
    stats = cache_manager.get_cache_stats()
    print(f"Memory warnings issued: {stats['maintenance']['memory_warnings']}")
    
    print("\n5. CACHE LIMITS AND EVICTION")
    print("-" * 40)
    
    # Fill cache to test eviction
    initial_count = stats['entries']['total']
    for i in range(20):  # Add more than max_entries
        cache_manager.set_piece(f"test_piece_{i}", {"id": i, "data": "x" * 100})
    
    stats_after = cache_manager.get_cache_stats()
    print(f"Entries before: {initial_count}")
    print(f"Entries after adding 20: {stats_after['entries']['total']}")
    print(f"Evictions performed: {stats_after['maintenance']['evictions']}")
    
    print("\n6. INTEGRATED DATA MANAGER DEMO")
    print("-" * 40)
    
    # Demonstrate the integrated data manager
    data_manager = CacheIntegratedDataManager()
    
    # Show cache integration
    cache_stats = data_manager.get_cache_stats()
    print(f"Integrated cache entries: {cache_stats['entries']['total']}")
    
    # Clear cache
    data_manager.clear_cache()
    print("✓ Cache cleared through data manager")
    
    print("\n" + "="*70)
    print("DEMO COMPLETED SUCCESSFULLY!")
    print("="*70)

def demonstrate_performance_comparison():
    """Compare performance with and without caching"""
    print("\n" + "="*70)
    print("CACHE PERFORMANCE COMPARISON")
    print("="*70)
    
    cache_manager = get_cache_manager()
    
    # Test data
    test_data = []
    for i in range(100):
        test_data.append({
            "name": f"Piece_{i}",
            "type": "knight" if i % 2 == 0 else "pawn",
            "abilities": [f"ability_{j}" for j in range(i % 5)],
            "stats": {"health": i * 10, "attack": i * 5},
            "description": f"This is test piece number {i} with various properties and abilities."
        })
    
    print("\n1. CACHE WRITE PERFORMANCE")
    print("-" * 40)
    
    # Time cache writes
    start_time = time.time()
    for i, data in enumerate(test_data):
        cache_manager.set_piece(f"perf_test_{i}", data)
    write_time = time.time() - start_time
    
    print(f"Cached 100 pieces in {write_time:.4f} seconds")
    print(f"Average write time: {write_time * 10:.2f} ms per piece")
    
    print("\n2. CACHE READ PERFORMANCE")
    print("-" * 40)
    
    # Time cache reads (hits)
    start_time = time.time()
    for i in range(100):
        cache_manager.get_piece(f"perf_test_{i}")
    read_time = time.time() - start_time
    
    print(f"Read 100 cached pieces in {read_time:.4f} seconds")
    print(f"Average read time: {read_time * 10:.2f} ms per piece")
    
    print("\n3. CACHE MISS PERFORMANCE")
    print("-" * 40)
    
    # Time cache misses
    start_time = time.time()
    for i in range(100, 200):
        cache_manager.get_piece(f"perf_test_{i}")
    miss_time = time.time() - start_time
    
    print(f"Handled 100 cache misses in {miss_time:.4f} seconds")
    print(f"Average miss time: {miss_time * 10:.2f} ms per miss")
    
    print("\n4. FINAL STATISTICS")
    print("-" * 40)
    
    stats = cache_manager.get_cache_stats()
    print(f"Total cache entries: {stats['entries']['total']}")
    print(f"Cache size: {stats['size']['total_mb']:.3f} MB")
    print(f"Hit rate: {stats['performance']['hit_rate']:.1%}")
    print(f"Total requests: {stats['performance']['total_requests']}")
    print(f"Cache hits: {stats['performance']['hits']}")
    print(f"Cache misses: {stats['performance']['misses']}")
    print(f"Evictions: {stats['maintenance']['evictions']}")
    print(f"Invalidations: {stats['maintenance']['invalidations']}")

def demonstrate_integration_patterns():
    """Show how to integrate with existing Adventure Chess Creator patterns"""
    print("\n" + "="*70)
    print("INTEGRATION PATTERNS FOR ADVENTURE CHESS CREATOR")
    print("="*70)
    
    print("\n1. REPLACING EXISTING DATA MANAGER")
    print("-" * 50)
    print("""
# OLD CODE (PydanticDataManager):
from schemas.data_manager import PydanticDataManager
data_manager = PydanticDataManager()

# NEW CODE (Enhanced with caching):
from enhanced_cache_manager import CacheIntegratedDataManager
data_manager = CacheIntegratedDataManager()

# Same interface, enhanced performance!
piece, error = data_manager.load_piece("my_piece.json")
success, error = data_manager.save_piece(piece_data, "my_piece.json")
""")
    
    print("\n2. MONITORING CACHE PERFORMANCE")
    print("-" * 50)
    print("""
# Get cache statistics for monitoring
stats = data_manager.get_cache_stats()
print(f"Cache hit rate: {stats['performance']['hit_rate']:.1%}")
print(f"Memory usage: {stats['size']['total_mb']:.2f} MB")

# Clear cache if needed
data_manager.clear_cache()
""")
    
    print("\n3. MANUAL CACHE INVALIDATION")
    print("-" * 50)
    print("""
# Invalidate cache when files are modified externally
cache_manager = get_cache_manager()
cache_manager.invalidate_file("/path/to/modified/file.json")

# Or clear all cache
cache_manager.clear_all()
""")
    
    print("\n4. CONFIGURATION OPTIONS")
    print("-" * 50)
    print("""
# Create cache manager with custom settings
from enhanced_cache_manager import EnhancedCacheManager

cache_manager = EnhancedCacheManager(
    max_cache_size_mb=200,          # 200 MB cache limit
    max_entries=2000,               # Maximum 2000 cached items
    cleanup_interval_seconds=600,   # Cleanup every 10 minutes
    memory_warning_threshold=0.85,  # Warn at 85% memory usage
    enable_file_watching=True       # Enable file modification detection
)
""")

if __name__ == "__main__":
    try:
        # Run all demonstrations
        demonstrate_cache_features()
        demonstrate_performance_comparison()
        demonstrate_integration_patterns()
        
        print("\n🎉 All demonstrations completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        raise
    finally:
        # Clean up
        cache_manager = get_cache_manager()
        cache_manager.shutdown()
        print("\n✓ Cache manager shutdown complete")
