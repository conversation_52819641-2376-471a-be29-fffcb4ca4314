#!/usr/bin/env python3
"""
Data Integrity Testing Suite for Adventure Chess Creator
Comprehensive test suite for save/load operations, corruption scenarios, and migration paths
"""

import os
import json
import tempfile
import shutil
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataIntegrityTestSuite:
    """
    Comprehensive data integrity testing suite
    Tests save/load operations, corruption scenarios, and migration paths
    """
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.temp_dir: Optional[str] = None
        
        # Sample test data
        self.sample_piece_data = {
            "version": "1.0.0",
            "name": "Test Piece",
            "description": "A test piece for validation",
            "role": "Commander",
            "canCapture": True,
            "movement": {
                "type": "orthogonal",
                "distance": 3
            },
            "abilities": ["Test Ability"],
            "recharge": {
                "type": "turnRecharge",
                "turns": 1
            }
        }
        
        self.sample_ability_data = {
            "version": "1.0.0",
            "name": "Test Ability",
            "description": "A test ability for validation",
            "cost": 2,
            "tags": ["move", "range"],
            "activationMode": "manual",
            "rangeMask": [[False for _ in range(8)] for _ in range(8)],
            "piecePosition": [4, 4]
        }
    
    def setup_test_environment(self):
        """Setup temporary test environment"""
        self.temp_dir = tempfile.mkdtemp(prefix="adventure_chess_test_")
        
        # Create test directories
        self.pieces_dir = os.path.join(self.temp_dir, "pieces")
        self.abilities_dir = os.path.join(self.temp_dir, "abilities")
        os.makedirs(self.pieces_dir, exist_ok=True)
        os.makedirs(self.abilities_dir, exist_ok=True)
        
        logger.info(f"Test environment setup at: {self.temp_dir}")
    
    def cleanup_test_environment(self):
        """Cleanup temporary test environment"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            logger.info(f"Test environment cleaned up: {self.temp_dir}")
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all data integrity tests"""
        logger.info("Starting comprehensive data integrity test suite...")
        
        try:
            self.setup_test_environment()
            
            # Run test categories
            self.test_basic_save_load_operations()
            self.test_file_corruption_scenarios()
            self.test_version_migration_paths()
            self.test_data_validation_scenarios()
            self.test_concurrent_access_scenarios()
            self.test_edge_cases_and_boundaries()
            
            # Generate summary
            summary = self.generate_test_summary()
            
            logger.info("Data integrity test suite completed successfully")
            return summary
            
        finally:
            self.cleanup_test_environment()
    
    def test_basic_save_load_operations(self):
        """Test basic save and load operations"""
        logger.info("Testing basic save/load operations...")
        
        test_cases = [
            ("piece_basic", self.sample_piece_data, self.pieces_dir),
            ("ability_basic", self.sample_ability_data, self.abilities_dir)
        ]
        
        for test_name, data, directory in test_cases:
            try:
                # Test save
                file_path = os.path.join(directory, f"{test_name}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)
                
                # Test load
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                # Validate data integrity
                assert loaded_data == data, f"Data mismatch in {test_name}"
                
                self._record_test_result(f"basic_save_load_{test_name}", True, "Success")
                
            except Exception as e:
                self._record_test_result(f"basic_save_load_{test_name}", False, str(e))
    
    def test_file_corruption_scenarios(self):
        """Test various file corruption scenarios"""
        logger.info("Testing file corruption scenarios...")
        
        corruption_scenarios = [
            ("empty_file", ""),
            ("invalid_json", "{ invalid json content"),
            ("truncated_json", '{"name": "Test", "desc'),
            ("wrong_encoding", b'\xff\xfe\x00\x00invalid'),
            ("missing_required_fields", '{"name": "Test"}'),
            ("invalid_data_types", '{"name": 123, "cost": "invalid"}'),
            ("malformed_arrays", '{"tags": [unclosed array}'),
            ("null_values", '{"name": null, "description": null}')
        ]
        
        for scenario_name, corrupt_content in corruption_scenarios:
            try:
                # Create corrupted file
                file_path = os.path.join(self.pieces_dir, f"corrupt_{scenario_name}.json")
                
                if isinstance(corrupt_content, bytes):
                    with open(file_path, 'wb') as f:
                        f.write(corrupt_content)
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(corrupt_content)
                
                # Attempt to load corrupted file
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        json.load(f)
                    
                    # If we get here, the corruption wasn't detected
                    self._record_test_result(f"corruption_{scenario_name}", False, "Corruption not detected")
                    
                except (json.JSONDecodeError, UnicodeDecodeError, ValueError) as e:
                    # Expected behavior - corruption detected
                    self._record_test_result(f"corruption_{scenario_name}", True, f"Corruption detected: {type(e).__name__}")
                
            except Exception as e:
                self._record_test_result(f"corruption_{scenario_name}", False, f"Test setup failed: {e}")
    
    def test_version_migration_paths(self):
        """Test migration from various version formats"""
        logger.info("Testing version migration paths...")
        
        # Legacy format examples (pre-1.0.0)
        legacy_formats = [
            ("legacy_no_version", {
                "name": "Legacy Piece",
                "description": "Old format piece",
                "role": "Commander"
                # Missing version field
            }),
            ("legacy_old_movement", {
                "version": "0.9.0",
                "name": "Old Movement Piece",
                "movementType": "orthogonal",  # Old field name
                "movementDistance": 3  # Old field name
            }),
            ("legacy_old_abilities", {
                "version": "0.8.0",
                "name": "Old Abilities Piece",
                "abilityList": ["old_ability_format"]  # Old field name
            })
        ]
        
        for test_name, legacy_data in legacy_formats:
            try:
                # Save legacy format
                file_path = os.path.join(self.pieces_dir, f"{test_name}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(legacy_data, f, indent=2)
                
                # Test migration (simulate loading with migration)
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                # Check if migration would be needed
                needs_migration = "version" not in loaded_data or loaded_data.get("version") != "1.0.0"
                
                self._record_test_result(f"migration_{test_name}", True, 
                                       f"Migration {'needed' if needs_migration else 'not needed'}")
                
            except Exception as e:
                self._record_test_result(f"migration_{test_name}", False, str(e))
    
    def test_data_validation_scenarios(self):
        """Test data validation edge cases"""
        logger.info("Testing data validation scenarios...")
        
        validation_test_cases = [
            ("empty_name", {**self.sample_piece_data, "name": ""}),
            ("null_name", {**self.sample_piece_data, "name": None}),
            ("invalid_role", {**self.sample_piece_data, "role": "InvalidRole"}),
            ("negative_cost", {**self.sample_ability_data, "cost": -1}),
            ("invalid_movement_type", {
                **self.sample_piece_data, 
                "movement": {"type": "invalid_type", "distance": 3}
            }),
            ("missing_movement", {k: v for k, v in self.sample_piece_data.items() if k != "movement"}),
            ("invalid_range_mask", {
                **self.sample_ability_data,
                "rangeMask": "invalid_mask_format"
            }),
            ("oversized_range_mask", {
                **self.sample_ability_data,
                "rangeMask": [[False for _ in range(10)] for _ in range(10)]  # Wrong size
            })
        ]
        
        for test_name, invalid_data in validation_test_cases:
            try:
                # Save invalid data
                file_path = os.path.join(self.pieces_dir, f"invalid_{test_name}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(invalid_data, f, indent=2)
                
                # Validation would happen during loading in real application
                # Here we just check if the data can be loaded as JSON
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                # Check for obvious validation issues
                validation_issues = []
                if not loaded_data.get("name"):
                    validation_issues.append("Empty or missing name")
                if "movement" not in loaded_data:
                    validation_issues.append("Missing movement data")
                
                self._record_test_result(f"validation_{test_name}", True, 
                                       f"Issues found: {validation_issues}" if validation_issues else "No obvious issues")
                
            except Exception as e:
                self._record_test_result(f"validation_{test_name}", False, str(e))
    
    def test_concurrent_access_scenarios(self):
        """Test concurrent file access scenarios"""
        logger.info("Testing concurrent access scenarios...")
        
        try:
            # Create test file
            file_path = os.path.join(self.pieces_dir, "concurrent_test.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.sample_piece_data, f, indent=2)
            
            # Test multiple read operations
            for i in range(5):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    assert data["name"] == "Test Piece"
            
            self._record_test_result("concurrent_read", True, "Multiple reads successful")
            
            # Test read while write (simplified simulation)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump({**self.sample_piece_data, "name": "Modified Test Piece"}, f, indent=2)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                assert data["name"] == "Modified Test Piece"
            
            self._record_test_result("concurrent_write_read", True, "Write then read successful")
            
        except Exception as e:
            self._record_test_result("concurrent_access", False, str(e))
    
    def test_edge_cases_and_boundaries(self):
        """Test edge cases and boundary conditions"""
        logger.info("Testing edge cases and boundaries...")
        
        edge_cases = [
            ("very_long_name", {
                **self.sample_piece_data, 
                "name": "A" * 1000  # Very long name
            }),
            ("unicode_characters", {
                **self.sample_piece_data,
                "name": "Test 🏰♔♕♖♗♘♙",
                "description": "Unicode test: αβγδε 中文 العربية"
            }),
            ("special_characters", {
                **self.sample_piece_data,
                "name": "Test!@#$%^&*()_+-=[]{}|;':\",./<>?"
            }),
            ("large_data_structure", {
                **self.sample_ability_data,
                "rangeMask": [[True for _ in range(8)] for _ in range(8)],
                "largeArray": list(range(1000))  # Large array
            }),
            ("deeply_nested", {
                **self.sample_piece_data,
                "nested": {
                    "level1": {
                        "level2": {
                            "level3": {
                                "level4": {
                                    "data": "deep nesting test"
                                }
                            }
                        }
                    }
                }
            })
        ]
        
        for test_name, edge_data in edge_cases:
            try:
                # Test save and load
                file_path = os.path.join(self.pieces_dir, f"edge_{test_name}.json")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(edge_data, f, indent=2, ensure_ascii=False)
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                # Basic integrity check
                assert loaded_data["name"] == edge_data["name"]
                
                self._record_test_result(f"edge_case_{test_name}", True, "Edge case handled successfully")
                
            except Exception as e:
                self._record_test_result(f"edge_case_{test_name}", False, str(e))
    
    def _record_test_result(self, test_name: str, success: bool, details: str):
        """Record a test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✓" if success else "✗"
        logger.info(f"{status} {test_name}: {details}")
    
    def generate_test_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        # Group results by category
        categories = {}
        for result in self.test_results:
            category = result["test_name"].split("_")[0]
            if category not in categories:
                categories[category] = {"passed": 0, "failed": 0, "tests": []}
            
            if result["success"]:
                categories[category]["passed"] += 1
            else:
                categories[category]["failed"] += 1
            
            categories[category]["tests"].append(result)
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "categories": categories,
            "failed_test_details": [r for r in self.test_results if not r["success"]],
            "timestamp": datetime.now().isoformat()
        }
        
        return summary

def run_data_integrity_tests():
    """Run the complete data integrity test suite"""
    print("=" * 70)
    print("ADVENTURE CHESS CREATOR - DATA INTEGRITY TEST SUITE")
    print("=" * 70)
    
    test_suite = DataIntegrityTestSuite()
    summary = test_suite.run_all_tests()
    
    # Print summary
    print(f"\nTEST SUMMARY:")
    print(f"Total Tests: {summary['total_tests']}")
    print(f"Passed: {summary['passed_tests']}")
    print(f"Failed: {summary['failed_tests']}")
    print(f"Success Rate: {summary['success_rate']:.1f}%")
    
    print(f"\nRESULTS BY CATEGORY:")
    for category, stats in summary['categories'].items():
        total = stats['passed'] + stats['failed']
        rate = (stats['passed'] / total * 100) if total > 0 else 0
        print(f"  {category.upper()}: {stats['passed']}/{total} ({rate:.1f}%)")
    
    if summary['failed_test_details']:
        print(f"\nFAILED TESTS:")
        for failure in summary['failed_test_details']:
            print(f"  ✗ {failure['test_name']}: {failure['details']}")
    
    print("=" * 70)
    
    return summary['failed_tests'] == 0

if __name__ == "__main__":
    success = run_data_integrity_tests()
    exit(0 if success else 1)
