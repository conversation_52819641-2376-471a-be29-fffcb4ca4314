#!/usr/bin/env python3
"""
Comprehensive Repair Script for Adventure Chess Creator
Addresses all identified issues from the file validation audit
"""

import json
import os
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveRepairTool:
    """Tool to repair all identified data integrity issues"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.pieces_dir = self.data_dir / "pieces"
        self.abilities_dir = self.data_dir / "abilities"
        
        # Missing abilities from logs
        self.missing_abilities = [
            "Magic_Bolt_1_Range",
            "Magic_Bolt_Front", 
            "summon_pawn",
            "teleport_basic"
        ]
        
        # Standard piece set for Adventure Chess
        self.standard_pieces = [
            "Adventure Pawn",
            "Adventure Rook", 
            "Adventure Knight",
            "Adventure Bishop",
            "Adventure Queen",
            "Adventure King"
        ]
    
    def run_comprehensive_repair(self):
        """Execute all repair operations"""
        logger.info("Starting comprehensive repair process...")
        
        # Create backup
        self.create_backup()
        
        # Ensure directory structure
        self.ensure_directory_structure()
        
        # Create missing ability files
        self.create_missing_abilities()
        
        # Create basic piece files if none exist
        self.create_basic_pieces()
        
        # Validate repairs
        self.validate_repairs()
        
        logger.info("Comprehensive repair completed successfully")
    
    def create_backup(self):
        """Create backup of current data"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = Path(f"data_backup_{timestamp}")
        
        if self.data_dir.exists():
            import shutil
            shutil.copytree(self.data_dir, backup_dir)
            logger.info(f"Backup created: {backup_dir}")
        else:
            logger.info("No data directory to backup")
    
    def ensure_directory_structure(self):
        """Ensure all required directories exist"""
        directories = [self.data_dir, self.pieces_dir, self.abilities_dir]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Ensured directory exists: {directory}")
    
    def create_missing_abilities(self):
        """Create the missing ability files referenced in logs"""
        logger.info("Creating missing ability files...")
        
        ability_templates = {
            "Magic_Bolt_1_Range": {
                "version": "1.0.0",
                "name": "Magic Bolt 1 Range",
                "description": "Magical projectile attack with 1 tile range",
                "cost": 1,
                "tags": ["capture", "range"],
                "activationMode": "click",
                "rangeMask": self._create_single_tile_range_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False,
                "captureTarget": "Enemy"
            },
            "Magic_Bolt_Front": {
                "version": "1.0.0", 
                "name": "Magic Bolt Front",
                "description": "Magical projectile that attacks the square directly in front",
                "cost": 1,
                "tags": ["capture", "range"],
                "activationMode": "click",
                "rangeMask": self._create_front_tile_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False,
                "captureTarget": "Enemy"
            },
            "summon_pawn": {
                "version": "1.0.0",
                "name": "Summon Pawn",
                "description": "Summon a friendly pawn on an adjacent square",
                "cost": 2,
                "tags": ["summon", "range"],
                "activationMode": "click",
                "rangeMask": self._create_adjacent_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": True,
                "rangeEnemyOnly": False,
                "summonMax": 1,
                "summonList": [
                    {
                        "piece": "Friendly Adventure Pawn",
                        "cost": 1
                    }
                ]
            },
            "teleport_basic": {
                "version": "1.0.0",
                "name": "Teleport Basic",
                "description": "Teleport to any empty square within 3 tiles",
                "cost": 2,
                "tags": ["move", "range"],
                "activationMode": "click",
                "rangeMask": self._create_teleport_pattern(),
                "piecePosition": [4, 4],
                "rangeFriendlyOnly": False,
                "rangeEnemyOnly": False
            }
        }
        
        for ability_name, ability_data in ability_templates.items():
            file_path = self.abilities_dir / f"{ability_name}.json"
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(ability_data, f, indent=2)
            
            logger.info(f"Created missing ability: {file_path}")
    
    def create_basic_pieces(self):
        """Create basic piece files if pieces directory is empty"""
        if not any(self.pieces_dir.glob("*.json")):
            logger.info("Creating basic piece files...")
            
            piece_templates = {
                "Adventure Pawn": {
                    "version": "1.0.0",
                    "name": "Adventure Pawn",
                    "description": "Basic pawn with enhanced abilities",
                    "role": "Soldier",
                    "can_castle": False,
                    "track_starting_position": True,
                    "color_directional": True,
                    "can_capture": True,
                    "movement": {
                        "type": "Custom",
                        "pattern": self._create_pawn_movement_pattern(),
                        "piece_position": [4, 4]
                    },
                    "recharge": {
                        "type": "None",
                        "turns": 0
                    },
                    "abilities": ["Pawns Magic Bolt"],
                    "promotions": ["Adventure Queen", "Adventure Rook", "Adventure Bishop", "Adventure Knight"]
                },
                "Adventure Rook": {
                    "version": "1.0.0",
                    "name": "Adventure Rook",
                    "description": "Powerful rook with special abilities",
                    "role": "Commander",
                    "can_castle": True,
                    "track_starting_position": False,
                    "color_directional": False,
                    "can_capture": True,
                    "movement": {
                        "type": "Orthogonal",
                        "distance": 8
                    },
                    "recharge": {
                        "type": "None",
                        "turns": 0
                    },
                    "abilities": [],
                    "promotions": []
                },
                "Adventure Knight": {
                    "version": "1.0.0",
                    "name": "Adventure Knight",
                    "description": "Agile knight with carrying abilities",
                    "role": "Commander",
                    "can_castle": False,
                    "track_starting_position": False,
                    "color_directional": False,
                    "can_capture": True,
                    "movement": {
                        "type": "L-shape",
                        "distance": 1
                    },
                    "recharge": {
                        "type": "None",
                        "turns": 0
                    },
                    "abilities": ["Knights Carry"],
                    "promotions": []
                },
                "Adventure Bishop": {
                    "version": "1.0.0",
                    "name": "Adventure Bishop", 
                    "description": "Mystical bishop with revival powers",
                    "role": "Commander",
                    "can_castle": False,
                    "track_starting_position": False,
                    "color_directional": False,
                    "can_capture": True,
                    "movement": {
                        "type": "Diagonal",
                        "distance": 8
                    },
                    "recharge": {
                        "type": "None",
                        "turns": 0
                    },
                    "abilities": ["Bishops revival", "Bishops Teleport home"],
                    "promotions": []
                },
                "Adventure Queen": {
                    "version": "1.0.0",
                    "name": "Adventure Queen",
                    "description": "Powerful queen with summoning abilities",
                    "role": "Commander",
                    "can_castle": False,
                    "track_starting_position": False,
                    "color_directional": False,
                    "can_capture": True,
                    "movement": {
                        "type": "Any",
                        "distance": 8
                    },
                    "recharge": {
                        "type": "None",
                        "turns": 0
                    },
                    "abilities": ["Queens sumon"],
                    "promotions": []
                },
                "Adventure King": {
                    "version": "1.0.0",
                    "name": "Adventure King",
                    "description": "Royal king with swapping abilities",
                    "role": "King",
                    "can_castle": True,
                    "track_starting_position": False,
                    "color_directional": False,
                    "can_capture": True,
                    "movement": {
                        "type": "Any",
                        "distance": 1
                    },
                    "recharge": {
                        "type": "None",
                        "turns": 0
                    },
                    "abilities": ["Kings Swap"],
                    "promotions": []
                }
            }
            
            for piece_name, piece_data in piece_templates.items():
                file_path = self.pieces_dir / f"{piece_name}.json"
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(piece_data, f, indent=2)
                
                logger.info(f"Created basic piece: {file_path}")
        else:
            logger.info("Piece files already exist, skipping creation")
    
    def _create_single_tile_range_pattern(self):
        """Create a pattern for single tile range around piece"""
        pattern = [[False for _ in range(8)] for _ in range(8)]
        # Adjacent squares around center (4,4)
        for row in range(3, 6):
            for col in range(3, 6):
                if row != 4 or col != 4:  # Don't include center
                    pattern[row][col] = True
        return pattern
    
    def _create_front_tile_pattern(self):
        """Create pattern for tile directly in front"""
        pattern = [[False for _ in range(8)] for _ in range(8)]
        pattern[3][4] = True  # One tile forward from center
        return pattern
    
    def _create_adjacent_pattern(self):
        """Create pattern for adjacent tiles"""
        pattern = [[False for _ in range(8)] for _ in range(8)]
        # 8 adjacent squares
        adjacent_positions = [
            (3, 3), (3, 4), (3, 5),
            (4, 3),         (4, 5),
            (5, 3), (5, 4), (5, 5)
        ]
        for row, col in adjacent_positions:
            pattern[row][col] = True
        return pattern
    
    def _create_teleport_pattern(self):
        """Create pattern for teleport range (3 tiles)"""
        pattern = [[False for _ in range(8)] for _ in range(8)]
        center_row, center_col = 4, 4
        
        for row in range(8):
            for col in range(8):
                distance = max(abs(row - center_row), abs(col - center_col))
                if 1 <= distance <= 3:  # Within 3 tiles but not on piece
                    pattern[row][col] = True
        return pattern
    
    def _create_pawn_movement_pattern(self):
        """Create custom movement pattern for adventure pawn"""
        pattern = [[False for _ in range(8)] for _ in range(8)]
        # Forward movement (one or two squares)
        pattern[3][4] = True  # One forward
        pattern[2][4] = True  # Two forward (for starting position)
        # Diagonal capture
        pattern[3][3] = True  # Diagonal left
        pattern[3][5] = True  # Diagonal right
        return pattern
    
    def validate_repairs(self):
        """Validate that all repairs were successful"""
        logger.info("Validating repairs...")
        
        # Check missing abilities were created
        for ability_name in self.missing_abilities:
            file_path = self.abilities_dir / f"{ability_name}.json"
            if file_path.exists():
                logger.info(f"✓ Created missing ability: {ability_name}")
            else:
                logger.error(f"✗ Failed to create ability: {ability_name}")
        
        # Check piece files exist
        piece_files = list(self.pieces_dir.glob("*.json"))
        logger.info(f"✓ Found {len(piece_files)} piece files")
        
        # Check ability files exist
        ability_files = list(self.abilities_dir.glob("*.json"))
        logger.info(f"✓ Found {len(ability_files)} ability files")
        
        logger.info("Repair validation completed")


if __name__ == "__main__":
    repair_tool = ComprehensiveRepairTool()
    repair_tool.run_comprehensive_repair()
