# Adventure Chess Glossary v1.0.2

!! This means a refactor is required. Make adjustments to the application, then read and update the readme in the glossary folder before making next glossary !!

## Table of Contents
1. [Quick Reference Index](#quick-reference-index)
2. [Backend Developer Index](#backend-developer-index)
   - [Core Ability Tags & Functions](#core-ability-tags--functions)
   - [Data Structure Reference](#data-structure-reference)
   - [Configuration Options Reference](#configuration-options-reference)
3. [Tooltip Reference Index](#tooltip-reference-index)
4. [Piece Editor Guide](#piece-editor-guide)
5. [Ability Editor Guide](#ability-editor-guide)
6. [Pattern & Range Editors](#pattern--range-editors)
7. [File Management](#file-management)
8. [Best Practices & Tips](#best-practices--tips)
9. [Troubleshooting](#troubleshooting)
10. [Version History](#version-history)


---


## Quick Reference Index


### Piece Editor Essentials
- **Name/Description**: Basic piece information
- **Role**: Commander (key piece) or Supporter (supports commander)
- **Icons**: Black and white piece icons with preview
- **Movement Types**: Orthogonal, Diagonal, Any, L-shape, Custom
- **Range Settings**: Distance values for each movement type
- **Capture Ability**: Yes/No radio buttons
- **Color Directional**: Piece behavior differs by color
- **Recharge System**: Points, starting points, recharge types
- **Abilities**: List of attached ability files
- **Promotions**: Primary and secondary promotion piece lists
- **Special Properties**: Can Castle, Track Starting Position

### Ability Editor Essentials
- **Action Tags**: move, summon, revival, capture, carryPiece, swapPlaces
- **Targeting Tags**: range, areaEffect
- **Condition Tags**: adjacencyRequired, losRequired, noTurnCost
- **Special Tags**: shareSpace, delay, passThrough
- **Quick Patterns**: ♜♝♛♞♚🌐 for instant range setting

### Pattern Editors
- **Pattern Editor**: 5-color system (Empty→Move→Attack→Both→Action→Any)
- **Range Editor**: 2-color system (In Range/Out of Range) + quick patterns
- **Both Editors**: Starting square and continue off board checkboxes


---


## Backend Developer Index


### Core Ability Tags & Functions !! Make sure each ability has Name, function, data(if any), options(if any), behavior, and expected ability interactions !!

#### **move**
- **Function**: Teleports piece to target square within range !! This ability allows your piece to move !!
- **Data**: `rangeMask`, `piecePosition` !! Has no Data Collected !!
- **Options**: `rangeFriendlyOnly`, `rangeEnemyOnly`, `rangeIncludeStart` !! Has no options !!

#### **summon**
- **Function**: Creates new pieces at target locations
- **Data**: `summonList` (array of piece objects), `summonMax` (integer)
- **Behavior**: Spawns pieces from list up to max limit per use

#### **revival**
- **Function**: Resurrects destroyed pieces at target locations
- **Data**: `revivalList` (array of piece objects), `revivalMax` (integer)
- **Options**: `revivalSacrificeMode`, `revivalMaxCost`, `revivalWithStartingPoints`

#### **capture**
- **Function**: Destroys pieces at target locations
- **Data**: `captureTarget` ("Enemy"|"Friendly"|"Any")
- **Behavior**: Removes pieces from board. 

#### **carryPiece**
- **Function**: Allows piece to carry other pieces while moving !! Add that when carrying a piece they count as 1 piece !!
- **Data**: `carryList` (array), `carryRange` (integer)
- **Options**: `carryDropOnDeath`, `carryDropMode`, `carryDropRange`, `carryDropCanCapture` !! Does not have these options in the application is this a duplicate !!

#### **swapPlaces**
- **Function**: Exchanges positions with target piece
- **Data**: `swapList` (array of valid swap targets)
- **Behavior**: Instant position exchange within range

#### **range**
- **Function**: Defines targeting area for abilities
- **Data**: `rangeMask` (8x8 boolean array), `piecePosition` ([row, col])
- **Options**: `rangeFriendlyOnly`, `rangeEnemyOnly`, `rangeIncludeStart` !! Remove include start option as it is handled in the range editor !!

#### **areaEffect**
- **Function**: Affects multiple squares around target
- **Data**: `areaShape` ("Circle"|"Square"|"Cross"|"Line"|"Custom")
- **Options**: `areaRadius` (integer), `areaEffectTarget`, `areaEffectCenter`, `customAreaPattern`

#### **adjacencyRequired**
- **Function**: Ability only works when adjacent to specific pieces
- **Data**: `adjacencyList` (array), `adjacencyDistance` (0-5)
- **Behavior**: Checks for required pieces within distance before activation

#### **losRequired**
- **Function**: Requires clear line of sight to target
- **Options**: `losIgnoreFriendly` (boolean) !! Verify this isn't duplicate , is supposed to also have ignore all !!
- **Behavior**: Validates clear path before ability activation !! use range modifier of ability first then defaults to piece movement. !!

#### **noTurnCost**
- **Function**: Ability doesn't consume turn points
- **Data**: `noTurnCostLimit` (0=unlimited, >0=uses per turn)
- **Behavior**: Allows multiple uses without ending turn

#### **shareSpace**
- **Function**: Multiple pieces can occupy same square
- **Data**: `shareSpaceMax` (2-8), `shareSpaceSameType` (boolean)
- **Behavior**: Overrides normal piece collision rules

#### **delay**
- **Function**: Ability effect occurs after specified turns
- **Data**: `delayTurns` (integer), `delayCancelable` (boolean) !! Verify this isn't a duplicate, this is supposed to have a delay actions spinner as an or option !!
- **Behavior**: Queues effect for future execution

#### **passThrough** !! Needs a range_editor_dialog.py here part of data !!
- **Function**: Can target through other pieces
- **Data**: `passThroughList` (array), `passThroughCapture` ("None"|"Enemy"|"Friendly"|"Any")
- **Behavior**: Ignores specified pieces for targeting

#### **displacePiece**
- **Function**: Pushes target piece in specified direction
- **Data**: `direction` (string), `distance` (integer), `displaceTargetList` (array)
- **Options**: `customDisplacementMap` (8x8 boolean array for custom positions) !! should use range_editor_dialog.py, remove custom displace map !!

#### **immobilize**
- **Function**: Prevents piece movement for specified turns
- **Data**: `duration` (integer), `immobilizeTargetList` (array)
- **Options**: `immobilizeDuration` (alternative duration setting) !! I dont see a secondary duration setting and we are not supposed to. verify this is noot a duplicate. !!

#### **pulseEffect**
- **Function**: Repeating effect that triggers every N turns
- **Data**: `interval` (integer - turns between activations) !! Add an action spiner, you can only adjust one. !!
- **Behavior**: Automatically re-triggers ability at set intervals

#### **revealTiles** !! This was removed and updated to be part the fog of war ability and is not an ability type. is this old code? !!
- **Function**: Makes hidden areas visible
- **Data**: `radius` (integer), `duration` (integer - turns visible)
- **Behavior**: Reveals fog of war or hidden tiles temporarily

#### **addObstacle**
- **Function**: Places obstacles on target squares
- **Data**: `type` ("Wall"|"Pit"|"Fire"|"Ice"|"Poison")
- **Behavior**: Creates terrain that blocks movement or damages pieces

#### **removeObstacle**
- **Function**: Removes obstacles from target squares
- **Data**: `type` ("Wall"|"Pit"|"Fire"|"Ice"|"Poison"|"Any")
- **Behavior**: Clears specified obstacle types from board

#### **duplicate**
- **Function**: Creates copies of piece at offset positions
- **Data**: `locationOffset` (array of [row, col] offsets)
- **Options**: `limit` (max duplicates), controlled by checkbox

#### **convertPiece**
- **Function**: Changes target pieces into different piece types !! this actually changes the color and thus side of a piece !!
- **Data**: `convertTargetList` (array of conversion rules)
- **Behavior**: Transforms pieces according to specified mappings

#### **reaction** !! Requires refactor, more info below and if not ask for clarity on what to do !!
- **Function**: Triggers automatically in response to events
- **Data**: `eventType` (trigger condition), `usesAction` (boolean)
- **Behavior**: Passive ability that activates on specific game events

#### **buffPiece**
- **Function**: Temporarily enhances target pieces
- **Data**: `buffTargetList`, `buffDuration`, `buffAbilities`, `buffMovementPattern`
- **Options**: `buffAdjustMovementAttack`, `buffMovementAttackPattern` !! Seems like we have duplicate options, this should be just one Pattern_editor_dialog.py to handle all move/atk !!

#### **debuffPiece**
- **Function**: Temporarily weakens target pieces
- **Data**: `debuffTargetList`, `debuffDuration`, `debuffPreventAbilities`
- **Options**: `debuffPreventLoS`, `debuffMovementPattern`, `debuffAdjustMovementAttack` !! Seems like we have duplicate options, this should be just one Pattern_editor_dialog.py 
                                                                                                      to handle all move/atk !!

#### **invisible**
- **Function**: Makes piece undetectable under certain conditions
- **Data**: `invisibilitySettings` object with reveal conditions
- **Options**: `revealOnMove`, `revealOnCapture`, `revealOnAction`, `revealOnEnemyLoS`

#### **trapTile**
- **Function**: Creates hidden traps on target squares
- **Data**: `trapEffects` object with effect definitions
- **Options**: `capture`, `immobilizeDuration`, `teleportRange`, `addAbility`

#### **fogOfWar**
- **Function**: Reveals hidden areas of the board
- **Data**: `visionType` ("sight"|"lantern"), `radius`, `duration`, `cost`
- **Options**: Custom range patterns for sight-based vision

#### **requiresStartingPosition**
- **Function**: Ability only works if piece hasn't moved from starting position
- **Behavior**: Validates piece is still at original spawn location


### Data Structure Reference

#### **Piece Object Structure**
```json
{
  "version": "string (1.0.0)",
  "name": "string",
  "description": "string",
  "role": "string (Commander|Supporter)",
  "canCastle": "boolean",
  "trackStartingPosition": "boolean",
  "blackIcon": "string (filename)",
  "whiteIcon": "string (filename)",
  "movement": {
    "type": "string (orthogonal|diagonal|any|l-shape|custom)",
    "distance": "integer (for standard types)",
    "pattern": "8x8 integer array (for custom)",
    "piece_position": "[row, col] array (for custom)"
  },
  "canCapture": "boolean",
  "colorDirectional": "boolean",
  "enableRecharge": "boolean",
  "maxPoints": "integer",
  "startingPoints": "integer",
  "rechargeType": "string (turnRecharge|adjacencyRecharge|committedRecharge)",
  "turnPoints": "integer",
  "abilities": "array of ability filenames",
  "promotions": "array of piece names",
  "promotions_2nd": "array of piece names"
}
```

#### **Ability Object Structure**
```json
{
  "name": "string",
  "description": "string",
  "cost": "integer",
  "activationMode": "string",
  "tags": "array of strings",
  "rangeMask": "8x8 boolean array",
  "piecePosition": "[row, col] array",
  "summonList": "array of piece objects",
  "revivalList": "array of piece objects",
  "carryList": "array of piece objects",
  "swapList": "array of piece objects",
  "adjacencyList": "array of piece objects",
  "passThroughList": "array of piece objects",
  "displaceTargetList": "array of piece objects",
  "immobilizeTargetList": "array of piece objects",
  "convertTargetList": "array of conversion rules",
  "buffTargetList": "array of piece objects",
  "debuffTargetList": "array of piece objects",
  "customAreaPattern": "8x8 boolean array",
  "customDisplacementMap": "8x8 boolean array",
  "buffMovementPattern": "8x8 boolean array",
  "debuffMovementPattern": "8x8 boolean array",
  "buffMovementAttackPattern": "8x8 boolean array",
  "debuffMovementAttackPattern": "8x8 boolean array"
}
```

#### **Pattern Array Values**
- **Movement Patterns (Piece Editor)**: 0=Empty, 1=Move, 2=Attack, 3=Both, 4=Action, 5=Any
- **Range Patterns (Ability Editor)**: true=In Range, false=Out of Range
- **Position Arrays**: [row, col] where row/col are 0-7 (top-left origin)
- **Custom Movement**: 8x8 integer array with piece_position for piece location


### Configuration Options Reference 

!! I only viewd about half them and they dont contain all the fields or have different fields then what s in the ability tag tab and then configuration tab. make sure these aren't duplicates and consolidate our ability editor !!

#### **Range Configuration**
- `rangeFriendlyOnly`: Ability only works on friendly side of map
- `rangeEnemyOnly`: Ability only works on enemy side of map
- `rangeIncludeStart`: Allow targeting piece's starting position !! remove !!

#### **Area Effect Configuration**
- `areaShape`: "Circle", "Square", "Cross", "Line", "Custom"
- `areaRadius`: Size of effect area (1-8)
- `areaEffectTarget`: [row, col] where ability targets
- `areaEffectCenter`: [row, col] center of effect area

#### **Summon/Revival Configuration**
- `summonMax`/`revivalMax`: Maximum pieces per use (1-10)
- `revivalSacrificeMode`: Use piece cost for revival !! If you dont have enough points you can sacrifice yourself to revive a piece !!
- `revivalMaxCost`: Maximum cost for sacrifice revival !! Points you can use during a sacrifice !!
- `revivalWithStartingPoints`: Revive with starting turn points !! Pieces you revive come back with at max this many points !!

#### **Carry Piece Configuration** !! Add these drop options to carry piece ability. and determine if this is duplicate  !!
- `carryRange`: Distance for picking up pieces (1-8) !! Make sure it is 0-8 !!
- `carryDropOnDeath`: Drop carried pieces when carrier dies
- `carryDropMode`: "random", "selectable", "self"
- `carryDropRange`: Distance for dropping pieces
- `carryDropCanCapture`: Dropped pieces can capture

#### **Adjacency Configuration** !! Update to match our ability tag. verify its not a duplicate !!
- `adjacencyDistance`: Maximum distance for adjacency (0-5)

#### **Line of Sight Configuration**
- `losIgnoreFriendly`: Ignore friendly pieces for LoS calculation !! Verify this isn't a duplicate it should also have ignore any !!

#### **No Turn Cost Configuration**
- `noTurnCostLimit`: Uses per turn (0=unlimited, 1-10=limited)

#### **Share Space Configuration**
- `shareSpaceMax`: Maximum pieces per square (2-8)
- `shareSpaceSameType`: Only same piece types can share

#### **Delay Configuration**
- `delayTurns`: Number of turns to delay (1-10)
- `delayCancelable`: Whether delay can be cancelled

#### **Pass Through Configuration** !! should be a piece selector and we need to add a range_editor_dialog !!
- `passThroughCapture`: "None", "Enemy", "Friendly", "Any"

#### **Displacement Configuration** !! verify this isn't a duplicate !!
- `direction`: "N", "NE", "E", "SE", "S", "SW", "W", "NW", "Random", "Away", "Toward"
- `distance`: Distance to push piece (1-8)

#### **Immobilize Configuration**
- `duration`/`immobilizeDuration`: Turns to immobilize (1-10)

#### **Pulse Effect Configuration**
- `interval`: Turns between activations (1-10)

#### **Reveal Tiles Configuration** !! This is old and need to be removed. verify its not in the code !!
- `radius`: Reveal radius (1-8)
- `duration`: Turns to stay revealed (1-10)

#### **Obstacle Configuration**
- `type`: "Wall", "Pit", "Fire", "Ice", "Poison", "Any" (for removal)

#### **Duplicate Configuration**
- `locationOffset`: Array of [row, col] relative positions
- `limit`: Maximum duplicates (1-8)

#### **Reaction Configuration**
- `eventType`: Trigger event type
- `usesAction`: Whether reaction consumes action points

#### **Buff/Debuff Configuration**
- `buffDuration`/`debuffDuration`: Effect duration (1-10)
- `buffAdjustMovementAttack`/`debuffAdjustMovementAttack`: Modify attack patterns
- `debuffPreventLoS`: Prevent line of sight abilities

#### **Invisibility Configuration**
- `revealOnMove`: Turns visible after moving
- `revealOnCapture`: Turns visible after capturing
- `revealOnAction`: Turns visible after using abilities
- `revealOnEnemyLoS`: Visible to enemies with line of sight

#### **Trap Configuration**
- `capture`: Trap destroys pieces
- `immobilizeDuration`: Turns to immobilize trapped pieces
- `teleportRange`: Distance for teleport traps
- `addAbility`: Ability name to grant to trapped pieces

#### **Fog of War Configuration**
- `visionType`: "sight", "lantern"
- `radius`: Vision radius (1-8)
- `duration`: Duration in turns (1-10)
- `cost`: Point cost per use

#### **Recharge System Configuration (Piece Editor)**
- `enableRecharge`: Enable the recharge system
- `maxPoints`: Maximum points piece can hold (0-99)
- `startingPoints`: Points piece starts with (0-99)
- `rechargeType`: "turnRecharge", "adjacencyRecharge", "committedRecharge"
- `turnPoints`: Points gained per turn (for turnRecharge)

#### **Movement Configuration (Piece Editor)**
- `type`: "orthogonal", "diagonal", "any", "l-shape", "custom"
- `distance`: Range for standard movement types (1-8)
- `pattern`: 8x8 array for custom movement
- `piece_position`: [row, col] for custom movement piece location

#### **Icon Configuration (Piece Editor)**
- `blackIcon`: Filename for black piece icon
- `whiteIcon`: Filename for white piece icon
- Icon preview and file picker available

#### **Role Configuration (Piece Editor)**
- `role`: "Commander" (key piece for check/checkmate) or "Supporter" (supports commander)
- `canCastle`: Special castling ability (only visible for Commander role)
- `trackStartingPosition`: Track if piece has moved from starting position

#### **Promotion Configuration (Piece Editor)**
- `promotions`: Array of primary promotion piece names
- `promotions_2nd`: Array of secondary promotion piece names
- Multi-selection dialogs with checkboxes for piece selection


---


## Tooltip Reference Index !! Add labels even if they dont have tooltips !!

### Piece Editor Tooltips
- **Name**: "Display name shown in game"
- **Description**: "Brief description of piece function"
- **Role**: "Commander: Your key piece affected by check/checkmate | Supporter: A supporter of your Commander"
- **Can Castle**: "Special castling ability (Commander only)"
- **Track Starting Position**: "Track if piece has moved from starting position"
- **Black/White Icon**: "Icon displayed for this piece color"
- **Movement Pattern**: "How the piece can move on the board"
- **Range Values**: "Distance piece can move in selected direction"
- **Can Capture**: "Whether piece can destroy enemies"
- **Color Directional**: "Piece behavior differs by color"
- **Enable Recharge System**: "Enable the points and recharge system for this piece"
- **Max Points**: "Maximum points piece can hold"
- **Starting Points**: "Points piece starts with"
- **Recharge Type**: "How piece regains points" !! this is displaying old info, make sure its not a duplicate !!
- **Turn Points**: "Points gained per turn"
- **Primary/Secondary Promotions**: "Pieces this can promote to"

### Ability Editor Tooltips
- **Name**: "Display name shown in game"
- **Description**: "Brief description of ability function"
- **Cost**: "Point cost to use ability"
- **Activation Mode**: "When/how ability can be activated"
- **Range Patterns**: "♜ Orthogonal lines | ♝ Diagonal lines | ♛ All directions | ♞ L-shaped | ♚ Adjacent | 🌐 Entire board"
- **Friendly Side Only**: "This ability can only be used on the friendly side of the map"
- **Enemy Side Only**: "This ability can only be used on the enemy side of the map"
- **Include Starting Tile**: "Allow targeting the piece's starting position"
- **Area Effect Shapes**: "Circle, Square, Cross, Line, or Custom pattern"
- **Max Distance**: "Maximum distance for adjacency"
- **Ignore Friendly**: "Ignore friendly pieces for line of sight calculation"
- **Uses per Turn**: "Maximum free uses per turn (0 = unlimited)"
- **Max Pieces**: "Maximum pieces per square"
- **Same Type Only**: "Only pieces of the same type can share"
- **Turns to Delay**: "Number of turns to delay effect"
- **Cancelable**: "Whether delay can be cancelled"

### Pattern Editor Tooltips !! It should have quick patterns. Verify its not a duplicate or old code !!
- **Include Starting Square**: "Allow targeting piece's starting position"
- **Continue Off Board**: "Allow targeting beyond board edges"
- **Color Cycling**: "Empty → Move → Attack → Both → Action → Any"

### Range Editor Tooltips
- **Include Starting Square**: "Allow targeting piece's starting position"
- **Continue Off Board**: "Allow targeting beyond board edges"
- **Quick Patterns**: "Click chess piece symbols for instant patterns"


---


## Piece Editor Guide

### Basic Setup
1. **Name & Description**: Enter piece identity and function
2. **Role**: Choose Commander (key piece) or Supporter
3. **Icons**: Select black and white piece icons

### Movement Configuration
1. **Movement Type**: Choose from Orthogonal, Diagonal, Any, L-shape, or Custom
2. **Range Values**: Set distance for standard movement types
3. **Custom Pattern**: Use pattern editor for complex movement (Custom type only)

### Properties & Features
- **Can Capture**: Enable if piece can destroy enemies
- **Color Directional**: Enable if piece behavior differs by color
- **Can Castle**: Special ability (Commander role only)
- **Track Starting Position**: Monitor if piece has moved

### Recharge System
1. **Enable Recharge**: Turn on points system
2. **Points Configuration**: Set max points and starting points
3. **Recharge Type**: Choose how piece regains points
   - **Turn Recharge**: Gain points each turn
   - **Adjacency Recharge**: Gain points when adjacent to specific pieces
   - **Committed Recharge**: Requires commitment to recharge

### Abilities & Promotions
1. **Abilities**: Attach ability files to piece
2. **Primary Promotions**: Select pieces for main promotion path
3. **Secondary Promotions**: Select pieces for alternative promotion path


---


## Ability Editor Guide

### Basic Setup
1. **Name & Description**: Enter ability identity and function
2. **Cost**: Set point cost for usage
3. **Activation Mode**: Choose when ability can be used

### Tag Selection
1. **Choose Action**: What the ability does (move, summon, capture, etc.)
2. **Add Targeting**: How to select targets (range, areaEffect)
3. **Set Conditions**: When ability works (adjacency, line of sight)
4. **Add Special Effects**: Extra behaviors (no turn cost, delay, etc.)

### Configuration
Each selected tag provides specific options:
- **Lists**: Select valid pieces for summon/revival/carry/swap
- **Ranges**: Set distances and targeting areas
- **Conditions**: Define requirements and restrictions


---


## Pattern & Range Editors

### Pattern Editor (5-Color System)
- **Empty (Gray)**: No action allowed
- **Move (Blue)**: Movement only
- **Attack (Red)**: Attack only  
- **Both (Purple)**: Move or attack
- **Action (Orange)**: Special action type !! make yellow !!
- **Any (Green)**: Any action type

**Usage**: Click tiles to cycle through colors. Right-click to move piece position !! add ability to click on one of the colors in the legend to start marking tiles that color. clicking on the tile again resumes current behavior. add clear tile to the legend !!

### Range Editor (2-Color System)
- **In Range (Green)**: Valid target squares
- **Out of Range (Gray)**: Invalid target squares
- **Piece Position (Blue)**: Current piece location

**Quick Patterns**:
- **♜ Rook**: Orthogonal lines (horizontal/vertical)
- **♝ Bishop**: Diagonal lines
- **♛ Queen**: All directions (rook + bishop)
- **♞ Knight**: L-shaped moves
- **♚ King**: Adjacent squares (8 directions)
- **🌐 Global**: Entire board except piece position

**Usage**: Click quick pattern buttons for instant setup, or manually click tiles. Right-click to move piece position.

### Common Options
- **Include Starting Square**: Allow targeting piece's own position
- **Continue Off Board**: Allow targeting beyond board boundaries


---


## Best Practices & Tips

### Piece Design
- **Balanced Cost**: Higher cost pieces should have proportionally stronger abilities
- **Clear Patterns**: Movement and attack patterns should be intuitive
- **Promotion Paths**: Consider upgrade strategies for long-term gameplay
- **Capture Rules**: Balance survivability with offensive capability

### Ability Design
- **Single Purpose**: Each ability should have one clear function
- **Appropriate Cost**: Balance power with point cost
- **Range Consideration**: Ensure range matches ability purpose
- **Tag Combinations**: Use multiple tags for complex behaviors

### Pattern Creation
- **Symmetry**: Consider symmetric patterns for balanced gameplay
- **Range Limits**: Avoid overly powerful global ranges
- **Visual Clarity**: Patterns should be easy to understand at a glance
- **Testing**: Verify patterns work as expected in different scenarios

### Performance Considerations
- **Large Patterns**: Complex 8x8 patterns may impact performance
- **Multiple Effects**: Abilities with many tags require more processing
- **Area Effects**: Large area effects can affect many pieces simultaneously
- **Pulse Effects**: Repeating abilities add ongoing computational load


---


## Troubleshooting

### Common Issues
- **Pattern Not Saving**: Ensure all required fields are filled
- **Ability Not Working**: Check that all required tags are selected
- **Range Issues**: Verify piece position is set correctly
- **File Loading Errors**: Check file format and compatibility

### Editor Problems
- **Piece Position Lost**: Use right-click to reposition piece in editors
- **Pattern Colors Wrong**: Click tiles to cycle through color options
- **Quick Patterns Not Working**: Ensure piece position is set before applying
- **Checkboxes Not Saving**: Verify checkbox states before saving

### Data Validation
- **Array Bounds**: Ensure all coordinates are within 0-7 range
- **Required Fields**: Name and description are mandatory for all objects
- **Tag Dependencies**: Some tags require specific data to function
- **Pattern Consistency**: Verify pattern arrays are 8x8 with valid values


---


## File Management

### Save System
- **Pieces**: Saved as `.json` files with all patterns and properties
- **Abilities**: Saved as `.json` files with all tags and configurations
- **Auto-backup**: Previous versions preserved when overwriting

### Load System
- **File Browser**: Navigate to saved pieces/abilities
- **Preview**: See basic information before loading
- **Validation**: Ensures file compatibility

### New Creation
- **Clean Slate**: Starts with empty patterns and default values
- **Template System**: Base configurations for common piece types


---


## Version History

### v1.0.2 (Current)
- **Comprehensive Documentation**: Multiple indexes for different user types
- **Backend Reference**: Technical details for developers
- **Tooltip Index**: Complete reference for all UI elements
- **Improved Organization**: Clear sections and navigation

### v1.0.1
- **Enhanced Editors**: 5-color pattern system, quick range patterns
- **Improved UX**: Selectable patterns, better synchronization
- **Cleaner Configuration**: Removed redundant options

### v1.0.0
- **Initial Release**: Basic piece and ability editors
- **Core Functionality**: Pattern creation, tag system, file management
