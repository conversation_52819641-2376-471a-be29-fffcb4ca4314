#!/usr/bin/env python3
# Backup script for Adventure Chess Creator data files
import shutil
from datetime import datetime
from pathlib import Path

def create_backup():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path(f"data_backup_{timestamp}")
    
    if Path("data").exists():
        shutil.copytree("data", backup_dir)
        print(f"Backup created: {backup_dir}")
    else:
        print("No data directory found")

if __name__ == "__main__":
    create_backup()
