#!/usr/bin/env python3
"""
Lazy Loading System for Adventure Chess Creator
Implements on-demand data loading, background loading, and progress indicators
"""

import os
import sys
import time
import logging
import threading
from typing import Dict, Any, Optional, List, Tuple, Callable, Union
from pathlib import Path
from datetime import datetime
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
from PyQt6.QtCore import QObject, pyqtSignal, QThread, QTimer
from PyQt6.QtWidgets import QProgressBar, QLabel, QWidget, QVBoxLayout

logger = logging.getLogger(__name__)

@dataclass
class LazyLoadRequest:
    """Represents a lazy loading request"""
    key: str
    load_function: Callable[[], Any]
    priority: int = 0  # Higher priority loads first
    callback: Optional[Callable[[Any], None]] = None
    error_callback: Optional[Callable[[Exception], None]] = None
    created_at: datetime = field(default_factory=datetime.now)
    
class LoadingProgressWidget(QWidget):
    """Widget to show loading progress with customizable appearance"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the loading progress UI"""
        layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3498db;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                background-color: #ecf0f1;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px;
                text-align: center;
            }
        """)
        
        layout.addWidget(self.status_label)
        layout.addWidget(self.progress_bar)
        self.setLayout(layout)
        
    def update_progress(self, value: int, message: str = ""):
        """Update progress bar and status message"""
        self.progress_bar.setValue(value)
        if message:
            self.status_label.setText(message)
            
    def show_loading(self, message: str = "Loading..."):
        """Show indeterminate loading state"""
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.status_label.setText(message)
        self.setVisible(True)
        
    def hide_loading(self):
        """Hide loading indicator"""
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(100)
        self.setVisible(False)

class BackgroundLoader(QThread):
    """Background thread for loading data without blocking UI"""
    
    progress_updated = pyqtSignal(int, str)  # progress, message
    item_loaded = pyqtSignal(str, object)    # key, data
    loading_finished = pyqtSignal(dict)      # results summary
    error_occurred = pyqtSignal(str, str)    # key, error_message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.requests: List[LazyLoadRequest] = []
        self.is_running = False
        self.should_stop = False
        
    def add_request(self, request: LazyLoadRequest):
        """Add a loading request to the queue"""
        self.requests.append(request)
        # Sort by priority (higher first)
        self.requests.sort(key=lambda r: r.priority, reverse=True)
        
    def run(self):
        """Execute loading requests in background"""
        self.is_running = True
        total_requests = len(self.requests)
        completed = 0
        results = {"success": 0, "errors": 0, "total": total_requests}
        
        try:
            for request in self.requests:
                if self.should_stop:
                    break
                    
                try:
                    # Update progress
                    progress = int((completed / total_requests) * 100) if total_requests > 0 else 0
                    self.progress_updated.emit(progress, f"Loading {request.key}...")
                    
                    # Execute load function
                    data = request.load_function()
                    
                    # Emit success
                    self.item_loaded.emit(request.key, data)
                    
                    # Call success callback if provided
                    if request.callback:
                        request.callback(data)
                        
                    results["success"] += 1
                    
                except Exception as e:
                    logger.error(f"Error loading {request.key}: {e}")
                    self.error_occurred.emit(request.key, str(e))
                    
                    # Call error callback if provided
                    if request.error_callback:
                        request.error_callback(e)
                        
                    results["errors"] += 1
                
                completed += 1
                
            # Final progress update
            if not self.should_stop:
                self.progress_updated.emit(100, f"Completed: {results['success']} loaded, {results['errors']} errors")
                
        finally:
            self.loading_finished.emit(results)
            self.is_running = False
            
    def stop_loading(self):
        """Stop the loading process"""
        self.should_stop = True

class LazyDataManager:
    """
    Manages lazy loading of data with caching and background loading capabilities
    """
    
    def __init__(self, cache_manager=None, max_workers: int = 4):
        self.cache_manager = cache_manager
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # Lazy loading state
        self.pending_requests: Dict[str, Future] = {}
        self.loading_callbacks: Dict[str, List[Callable]] = {}
        
        # File metadata cache for quick access
        self.file_metadata: Dict[str, Dict[str, Any]] = {}
        self.metadata_loaded = False
        
        # Background loader for UI integration
        self.background_loader: Optional[BackgroundLoader] = None
        
    def load_file_metadata_lazy(self, directory: str, extension: str = ".json") -> List[Dict[str, Any]]:
        """
        Load file metadata (name, size, modified time) without loading full content
        This provides quick file listing for UI components
        """
        try:
            if not os.path.exists(directory):
                return []
                
            metadata_list = []
            dir_path = Path(directory)
            
            for file_path in dir_path.glob(f"*{extension}"):
                try:
                    stat = file_path.stat()
                    metadata = {
                        "filename": file_path.stem,
                        "full_path": str(file_path),
                        "size_bytes": stat.st_size,
                        "modified_time": datetime.fromtimestamp(stat.st_mtime),
                        "is_loaded": False,
                        "has_error": False
                    }
                    
                    # Try to get basic info from cache if available
                    if self.cache_manager:
                        cache_key = str(file_path)
                        if file_path.name.endswith('.json'):
                            if 'piece' in directory.lower():
                                cached_data = self.cache_manager.get_piece(cache_key)
                            else:
                                cached_data = self.cache_manager.get_ability(cache_key)
                            
                            if cached_data:
                                metadata["is_loaded"] = True
                                metadata["display_name"] = cached_data.get("name", file_path.stem)
                                metadata["description"] = cached_data.get("description", "")
                    
                    metadata_list.append(metadata)
                    
                except Exception as e:
                    logger.warning(f"Error reading metadata for {file_path}: {e}")
                    metadata_list.append({
                        "filename": file_path.stem,
                        "full_path": str(file_path),
                        "size_bytes": 0,
                        "modified_time": datetime.now(),
                        "is_loaded": False,
                        "has_error": True,
                        "error": str(e)
                    })
            
            # Sort by modification time (newest first)
            metadata_list.sort(key=lambda x: x["modified_time"], reverse=True)
            return metadata_list
            
        except Exception as e:
            logger.error(f"Error loading file metadata from {directory}: {e}")
            return []
    
    def load_data_lazy(self, key: str, load_function: Callable[[], Any], 
                      callback: Optional[Callable[[Any], None]] = None,
                      priority: int = 0) -> Optional[Any]:
        """
        Load data lazily - returns immediately if cached, otherwise loads in background
        """
        # Check cache first
        if self.cache_manager:
            if 'piece' in key.lower():
                cached_data = self.cache_manager.get_piece(key)
            else:
                cached_data = self.cache_manager.get_ability(key)
                
            if cached_data is not None:
                if callback:
                    callback(cached_data)
                return cached_data
        
        # Check if already loading
        if key in self.pending_requests:
            # Add callback to existing request
            if callback:
                if key not in self.loading_callbacks:
                    self.loading_callbacks[key] = []
                self.loading_callbacks[key].append(callback)
            return None
        
        # Start new loading request
        future = self.executor.submit(self._load_with_caching, key, load_function)
        self.pending_requests[key] = future
        
        if callback:
            self.loading_callbacks[key] = [callback]
        
        # Handle completion
        def on_complete(fut):
            try:
                data = fut.result()
                # Call all callbacks
                if key in self.loading_callbacks:
                    for cb in self.loading_callbacks[key]:
                        try:
                            cb(data)
                        except Exception as e:
                            logger.error(f"Error in callback for {key}: {e}")
                    del self.loading_callbacks[key]
            except Exception as e:
                logger.error(f"Error loading {key}: {e}")
                # Call error callbacks if any
                if key in self.loading_callbacks:
                    del self.loading_callbacks[key]
            finally:
                if key in self.pending_requests:
                    del self.pending_requests[key]
        
        future.add_done_callback(on_complete)
        return None
    
    def _load_with_caching(self, key: str, load_function: Callable[[], Any]) -> Any:
        """Load data and cache it"""
        try:
            data = load_function()
            
            # Cache the data
            if self.cache_manager and data is not None:
                if 'piece' in key.lower():
                    self.cache_manager.set_piece(key, data)
                else:
                    self.cache_manager.set_ability(key, data)
            
            return data
            
        except Exception as e:
            logger.error(f"Error in _load_with_caching for {key}: {e}")
            raise
    
    def preload_files(self, file_list: List[str], load_functions: Dict[str, Callable],
                     progress_callback: Optional[Callable[[int, str], None]] = None) -> None:
        """
        Preload multiple files in background with progress tracking
        """
        if not file_list:
            return
            
        def preload_worker():
            total = len(file_list)
            for i, filename in enumerate(file_list):
                try:
                    if filename in load_functions:
                        load_func = load_functions[filename]
                        self._load_with_caching(filename, load_func)
                        
                    if progress_callback:
                        progress = int((i + 1) / total * 100)
                        progress_callback(progress, f"Preloaded {filename}")
                        
                except Exception as e:
                    logger.error(f"Error preloading {filename}: {e}")
                    if progress_callback:
                        progress = int((i + 1) / total * 100)
                        progress_callback(progress, f"Error loading {filename}")
        
        # Run preloading in background
        self.executor.submit(preload_worker)
    
    def is_loading(self, key: str) -> bool:
        """Check if a specific key is currently being loaded"""
        return key in self.pending_requests
    
    def get_loading_status(self) -> Dict[str, Any]:
        """Get current loading status"""
        return {
            "pending_requests": len(self.pending_requests),
            "active_callbacks": len(self.loading_callbacks),
            "pending_keys": list(self.pending_requests.keys())
        }
    
    def shutdown(self):
        """Shutdown the lazy data manager"""
        try:
            # Cancel pending requests
            for future in self.pending_requests.values():
                future.cancel()

            # Shutdown executor (timeout parameter added in Python 3.9)
            try:
                self.executor.shutdown(wait=True, timeout=5.0)
            except TypeError:
                # Fallback for older Python versions
                self.executor.shutdown(wait=True)

            # Stop background loader if running
            if self.background_loader and self.background_loader.isRunning():
                self.background_loader.stop_loading()
                self.background_loader.wait(3000)  # Wait up to 3 seconds

        except Exception as e:
            logger.error(f"Error shutting down lazy data manager: {e}")

# Global lazy data manager instance
_lazy_manager: Optional[LazyDataManager] = None

def get_lazy_manager(cache_manager=None) -> LazyDataManager:
    """Get or create the global lazy data manager"""
    global _lazy_manager
    if _lazy_manager is None:
        _lazy_manager = LazyDataManager(cache_manager)
    return _lazy_manager
