#!/usr/bin/env python3
"""
File Validation Audit Script for Adventure Chess Creator
Scans all piece and ability files for corruption, missing version fields, and other issues
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileValidationAuditor:
    """Comprehensive file validation and repair system"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.pieces_dir = self.data_dir / "pieces"
        self.abilities_dir = self.data_dir / "abilities"
        
        # Results tracking
        self.validation_results = {
            "pieces": {"valid": [], "invalid": [], "missing_version": [], "corrupted": []},
            "abilities": {"valid": [], "invalid": [], "missing_version": [], "corrupted": []},
            "temp_files": [],
            "missing_references": []
        }
        
        # Known issues from logs
        self.known_missing_abilities = [
            "Magic_Bolt_1_Range", "Magic_Bolt_Front", "summon_pawn", "teleport_basic"
        ]
    
    def run_full_audit(self) -> Dict[str, Any]:
        """Run complete file validation audit"""
        logger.info("Starting comprehensive file validation audit...")
        
        # Check directory structure
        self._check_directory_structure()
        
        # Validate piece files
        self._validate_pieces()
        
        # Validate ability files
        self._validate_abilities()
        
        # Check for temporary files
        self._check_temporary_files()
        
        # Check for missing references
        self._check_missing_references()
        
        # Generate report
        report = self._generate_audit_report()
        
        logger.info("File validation audit completed")
        return report
    
    def _check_directory_structure(self):
        """Verify directory structure exists"""
        logger.info("Checking directory structure...")
        
        if not self.data_dir.exists():
            logger.error(f"Data directory not found: {self.data_dir}")
            return
        
        if not self.pieces_dir.exists():
            logger.warning(f"Pieces directory not found: {self.pieces_dir}")
            self.pieces_dir.mkdir(parents=True, exist_ok=True)
        
        if not self.abilities_dir.exists():
            logger.warning(f"Abilities directory not found: {self.abilities_dir}")
            self.abilities_dir.mkdir(parents=True, exist_ok=True)
    
    def _validate_pieces(self):
        """Validate all piece files"""
        logger.info("Validating piece files...")
        
        if not self.pieces_dir.exists():
            logger.warning("No pieces directory found")
            return
        
        piece_files = list(self.pieces_dir.glob("*.json"))
        logger.info(f"Found {len(piece_files)} piece files")
        
        for piece_file in piece_files:
            try:
                result = self._validate_json_file(piece_file, "piece")
                category = result["status"]
                self.validation_results["pieces"][category].append(result)
                
            except Exception as e:
                logger.error(f"Error validating piece file {piece_file}: {e}")
                self.validation_results["pieces"]["corrupted"].append({
                    "file": str(piece_file),
                    "error": str(e),
                    "status": "corrupted"
                })
    
    def _validate_abilities(self):
        """Validate all ability files"""
        logger.info("Validating ability files...")
        
        if not self.abilities_dir.exists():
            logger.warning("No abilities directory found")
            return
        
        ability_files = list(self.abilities_dir.glob("*.json"))
        logger.info(f"Found {len(ability_files)} ability files")
        
        for ability_file in ability_files:
            try:
                result = self._validate_json_file(ability_file, "ability")
                category = result["status"]
                self.validation_results["abilities"][category].append(result)
                
            except Exception as e:
                logger.error(f"Error validating ability file {ability_file}: {e}")
                self.validation_results["abilities"]["corrupted"].append({
                    "file": str(ability_file),
                    "error": str(e),
                    "status": "corrupted"
                })
    
    def _validate_json_file(self, file_path: Path, file_type: str) -> Dict[str, Any]:
        """Validate individual JSON file"""
        result = {
            "file": str(file_path),
            "type": file_type,
            "status": "valid",
            "issues": [],
            "data": None
        }
        
        try:
            # Load JSON
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            result["data"] = data
            
            # Check for version field
            if "version" not in data:
                result["issues"].append("Missing version field")
                result["status"] = "missing_version"
            
            # Check required fields based on type
            if file_type == "piece":
                required_fields = ["name"]
                for field in required_fields:
                    if field not in data:
                        result["issues"].append(f"Missing required field: {field}")
                        result["status"] = "invalid"
            
            elif file_type == "ability":
                required_fields = ["name"]
                for field in required_fields:
                    if field not in data:
                        result["issues"].append(f"Missing required field: {field}")
                        result["status"] = "invalid"
            
            # Check for temporary file patterns
            if file_path.name.startswith("tmp"):
                result["issues"].append("Temporary file detected")
                result["status"] = "invalid"
            
            # Validate JSON structure
            if not isinstance(data, dict):
                result["issues"].append("Invalid JSON structure - not a dictionary")
                result["status"] = "invalid"
            
        except json.JSONDecodeError as e:
            result["issues"].append(f"JSON decode error: {e}")
            result["status"] = "corrupted"
        except Exception as e:
            result["issues"].append(f"File read error: {e}")
            result["status"] = "corrupted"
        
        return result
    
    def _check_temporary_files(self):
        """Check for temporary files that should be cleaned up"""
        logger.info("Checking for temporary files...")
        
        # Check abilities directory for temp files
        if self.abilities_dir.exists():
            temp_files = list(self.abilities_dir.glob("tmp*"))
            for temp_file in temp_files:
                self.validation_results["temp_files"].append(str(temp_file))
                logger.warning(f"Found temporary file: {temp_file}")
    
    def _check_missing_references(self):
        """Check for missing ability references from logs"""
        logger.info("Checking for missing ability references...")
        
        for missing_ability in self.known_missing_abilities:
            ability_file = self.abilities_dir / f"{missing_ability}.json"
            if not ability_file.exists():
                self.validation_results["missing_references"].append(missing_ability)
                logger.warning(f"Missing referenced ability: {missing_ability}")
    
    def _generate_audit_report(self) -> Dict[str, Any]:
        """Generate comprehensive audit report"""
        logger.info("Generating audit report...")
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "pieces": {
                    "total": sum(len(files) for files in self.validation_results["pieces"].values()),
                    "valid": len(self.validation_results["pieces"]["valid"]),
                    "invalid": len(self.validation_results["pieces"]["invalid"]),
                    "missing_version": len(self.validation_results["pieces"]["missing_version"]),
                    "corrupted": len(self.validation_results["pieces"]["corrupted"])
                },
                "abilities": {
                    "total": sum(len(files) for files in self.validation_results["abilities"].values()),
                    "valid": len(self.validation_results["abilities"]["valid"]),
                    "invalid": len(self.validation_results["abilities"]["invalid"]),
                    "missing_version": len(self.validation_results["abilities"]["missing_version"]),
                    "corrupted": len(self.validation_results["abilities"]["corrupted"])
                },
                "temp_files": len(self.validation_results["temp_files"]),
                "missing_references": len(self.validation_results["missing_references"])
            },
            "details": self.validation_results
        }
        
        return report
    
    def create_repair_scripts(self, report: Dict[str, Any]):
        """Create repair scripts for identified issues"""
        logger.info("Creating repair scripts...")
        
        # Create backup script
        self._create_backup_script()
        
        # Create version field repair script
        self._create_version_repair_script(report)
        
        # Create temp file cleanup script
        self._create_temp_cleanup_script(report)
        
        logger.info("Repair scripts created")
    
    def _create_backup_script(self):
        """Create backup script for data files"""
        backup_script = """#!/usr/bin/env python3
# Backup script for Adventure Chess Creator data files
import shutil
from datetime import datetime
from pathlib import Path

def create_backup():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path(f"data_backup_{timestamp}")
    
    if Path("data").exists():
        shutil.copytree("data", backup_dir)
        print(f"Backup created: {backup_dir}")
    else:
        print("No data directory found")

if __name__ == "__main__":
    create_backup()
"""
        
        with open("backup_data.py", "w") as f:
            f.write(backup_script)
        
        logger.info("Created backup script: backup_data.py")

    def _create_version_repair_script(self, report: Dict[str, Any]):
        """Create script to add version fields to files missing them"""
        repair_script = '''#!/usr/bin/env python3
# Version field repair script for Adventure Chess Creator
import json
from pathlib import Path

def repair_version_fields():
    """Add version fields to files missing them"""

    # Files missing version fields from audit
    missing_version_files = []
'''

        # Add missing version files from report
        for file_info in report["details"]["pieces"]["missing_version"]:
            repair_script += f'    missing_version_files.append("{file_info["file"]}")\n'

        for file_info in report["details"]["abilities"]["missing_version"]:
            repair_script += f'    missing_version_files.append("{file_info["file"]}")\n'

        repair_script += '''

    for file_path in missing_version_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Add version field if missing
            if "version" not in data:
                data["version"] = "1.0.0"

                # Create backup
                backup_path = file_path + ".backup"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)

                # Save repaired file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2)

                print(f"Repaired version field in: {file_path}")

        except Exception as e:
            print(f"Error repairing {file_path}: {e}")

if __name__ == "__main__":
    repair_version_fields()
'''

        with open("repair_version_fields.py", "w") as f:
            f.write(repair_script)

        logger.info("Created version repair script: repair_version_fields.py")

    def _create_temp_cleanup_script(self, report: Dict[str, Any]):
        """Create script to clean up temporary files"""
        cleanup_script = '''#!/usr/bin/env python3
# Temporary file cleanup script for Adventure Chess Creator
import os
from pathlib import Path

def cleanup_temp_files():
    """Remove temporary files identified in audit"""

    temp_files = []
'''

        # Add temp files from report
        for temp_file in report["details"]["temp_files"]:
            cleanup_script += f'    temp_files.append("{temp_file}")\n'

        cleanup_script += '''

    for temp_file in temp_files:
        try:
            if Path(temp_file).exists():
                os.remove(temp_file)
                print(f"Removed temporary file: {temp_file}")
            else:
                print(f"Temporary file not found: {temp_file}")
        except Exception as e:
            print(f"Error removing {temp_file}: {e}")

if __name__ == "__main__":
    cleanup_temp_files()
'''

        with open("cleanup_temp_files.py", "w") as f:
            f.write(cleanup_script)

        logger.info("Created temp cleanup script: cleanup_temp_files.py")


if __name__ == "__main__":
    auditor = FileValidationAuditor()
    audit_report = auditor.run_full_audit()
    
    # Print summary
    print("\n" + "="*50)
    print("FILE VALIDATION AUDIT REPORT")
    print("="*50)
    
    print(f"\nPieces Summary:")
    print(f"  Total: {audit_report['summary']['pieces']['total']}")
    print(f"  Valid: {audit_report['summary']['pieces']['valid']}")
    print(f"  Missing Version: {audit_report['summary']['pieces']['missing_version']}")
    print(f"  Invalid: {audit_report['summary']['pieces']['invalid']}")
    print(f"  Corrupted: {audit_report['summary']['pieces']['corrupted']}")
    
    print(f"\nAbilities Summary:")
    print(f"  Total: {audit_report['summary']['abilities']['total']}")
    print(f"  Valid: {audit_report['summary']['abilities']['valid']}")
    print(f"  Missing Version: {audit_report['summary']['abilities']['missing_version']}")
    print(f"  Invalid: {audit_report['summary']['abilities']['invalid']}")
    print(f"  Corrupted: {audit_report['summary']['abilities']['corrupted']}")
    
    print(f"\nOther Issues:")
    print(f"  Temporary Files: {audit_report['summary']['temp_files']}")
    print(f"  Missing References: {audit_report['summary']['missing_references']}")
    
    # Save detailed report
    with open("audit_report.json", "w") as f:
        json.dump(audit_report, f, indent=2)
    
    print(f"\nDetailed report saved to: audit_report.json")
    
    # Create repair scripts
    auditor.create_repair_scripts(audit_report)
