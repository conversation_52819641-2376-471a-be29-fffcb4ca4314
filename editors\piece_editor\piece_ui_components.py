"""
Piece Editor UI Components for Adventure Chess Creator

This module contains all UI creation and layout logic for the piece editor:
- Main UI initialization and layout
- Widget creation and styling
- Form sections and grouping
- Responsive layout handling
- Button creation and configuration

Extracted from piece_editor.py to improve maintainability and separate
UI concerns from business logic.
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton,
    QComboBox, QGroupBox, QSpinBox, QCheckBox, QScrollArea, QGridLayout,
    QFormLayout, QListWidget, QSizePolicy
)
from PyQt6.QtCore import Qt

# Local imports
from config import PIECE_ROLES, RECHARGE_TYPES
from ui.ui_utils import ResponsiveLayout, ResponsiveScrollArea, make_widget_responsive
from ui.ui_shared_components import FileOperationsWidget, ValidationStatusWidget

logger = logging.getLogger(__name__)


class PieceUIComponents:
    """
    Handles all UI component creation and layout for the piece editor.
    
    This class separates UI creation from business logic, making the code
    more maintainable and easier to modify.
    """
    
    def __init__(self, editor_instance):
        """
        Initialize UI components handler.
        
        Args:
            editor_instance: The PieceEditorWindow instance
        """
        self.editor = editor_instance
    
    def init_main_ui(self) -> QWidget:
        """
        Initialize the main user interface.
        
        Returns:
            The central widget for the main window
        """
        try:
            # Create main widget with scroll area
            scroll_area = ResponsiveScrollArea()
            main_layout = scroll_area.content_layout
            
            # Top toolbar with file operations
            toolbar_layout = self._create_toolbar()
            main_layout.addLayout(toolbar_layout)
            
            # Basic piece information section
            basic_info_section = self._create_basic_info_section()
            main_layout.addWidget(basic_info_section)
            
            # Movement section
            movement_section = self._create_movement_section()
            main_layout.addWidget(movement_section)
            
            # Abilities section
            abilities_section = self._create_abilities_section()
            main_layout.addWidget(abilities_section)
            
            # Promotion section
            promotion_section = self._create_promotion_section()
            main_layout.addWidget(promotion_section)
            
            # Points and recharge section
            points_section = self._create_points_section()
            main_layout.addWidget(points_section)
            
            # Icon section
            icon_section = self._create_icon_section()
            main_layout.addWidget(icon_section)
            
            # Status display
            self.editor.status_widget = ValidationStatusWidget()
            main_layout.addWidget(self.editor.status_widget)
            
            # Console log
            console_section = self._create_console_section()
            main_layout.addWidget(console_section)
            
            logger.info("Main UI initialized successfully")
            return scroll_area
            
        except Exception as e:
            logger.error(f"Error initializing main UI: {e}")
            raise
    
    def _create_toolbar(self) -> QHBoxLayout:
        """
        Create the top toolbar with file operations.
        
        Returns:
            The toolbar layout
        """
        toolbar_layout = QHBoxLayout()
        
        # File operations widget
        self.editor.file_ops_widget = FileOperationsWidget(
            button_set="full",
            parent=self.editor
        )
        toolbar_layout.addWidget(self.editor.file_ops_widget)
        
        # Load piece dropdown
        load_layout = QHBoxLayout()
        load_layout.addWidget(QLabel("Quick Load:"))
        
        self.editor.load_piece_combo = QComboBox()
        self.editor.load_piece_combo.addItem("Select piece to load...")
        self.editor.load_piece_combo.currentTextChanged.connect(
            self.editor.data_handler.load_piece_from_dropdown
        )
        load_layout.addWidget(self.editor.load_piece_combo)
        
        toolbar_layout.addLayout(load_layout)
        toolbar_layout.addStretch()
        
        return toolbar_layout
    
    def _create_basic_info_section(self) -> QGroupBox:
        """
        Create the basic piece information section.
        
        Returns:
            The basic info group box
        """
        group = QGroupBox("Basic Piece Information")
        layout = QFormLayout()
        
        # Name field
        self.editor.name_edit = QLineEdit()
        self.editor.name_edit.setPlaceholderText("Enter piece name...")
        layout.addRow("Name:", self.editor.name_edit)
        
        # Description field
        self.editor.description_edit = QTextEdit()
        self.editor.description_edit.setPlaceholderText("Enter piece description...")
        self.editor.description_edit.setMaximumHeight(80)
        layout.addRow("Description:", self.editor.description_edit)
        
        # Role selection
        self.editor.role_combo = QComboBox()
        for role_name, _ in PIECE_ROLES:
            self.editor.role_combo.addItem(role_name)
        self.editor.role_combo.currentTextChanged.connect(self.editor.on_role_changed)
        layout.addRow("Role:", self.editor.role_combo)
        
        # Checkboxes for piece properties
        checkbox_layout = QHBoxLayout()
        
        self.editor.can_castle_check = QCheckBox("Can Castle")
        checkbox_layout.addWidget(self.editor.can_castle_check)
        
        self.editor.can_capture_check = QCheckBox("Can Capture")
        self.editor.can_capture_check.setChecked(True)  # Default to true
        checkbox_layout.addWidget(self.editor.can_capture_check)
        
        self.editor.color_directional_check = QCheckBox("Color Directional")
        checkbox_layout.addWidget(self.editor.color_directional_check)
        
        checkbox_layout.addStretch()
        layout.addRow("Properties:", checkbox_layout)
        
        group.setLayout(layout)
        return group
    
    def _create_movement_section(self) -> QGroupBox:
        """
        Create the movement pattern section.
        
        Returns:
            The movement group box
        """
        group = QGroupBox("Movement Pattern")
        layout = QVBoxLayout()
        
        # Movement type selection buttons
        pattern_layout = QHBoxLayout()
        pattern_layout.addWidget(QLabel("Movement Type:"))
        
        # Quick pattern buttons with chess piece symbols
        self.editor.movement_pattern_buttons = []
        movement_buttons = [
            ("♜", "orthogonal", "Orthogonal lines (like Rook)"),
            ("♝", "diagonal", "Diagonal lines (like Bishop)"),
            ("♛", "any", "All directions (like Queen)"),
            ("♞", "lShape", "L-shaped moves (like Knight)"),
            ("♚", "king", "King movement (1 square any direction)"),
            ("🌐", "global", "Global movement (anywhere on board)"),
            ("🎨", "custom", "Custom pattern (opens pattern editor)")
        ]
        
        for symbol, movement_type, tooltip in movement_buttons:
            btn = QPushButton(symbol)
            btn.setFixedSize(35, 30)
            btn.setToolTip(f"{tooltip}")
            btn.setCheckable(True)
            btn.setStyleSheet(self._get_movement_button_style())
            btn.clicked.connect(lambda checked, mt=movement_type: self.editor.on_movement_pattern_selected(mt))
            
            self.editor.movement_pattern_buttons.append((btn, movement_type))
            pattern_layout.addWidget(btn)
        
        pattern_layout.addStretch()
        layout.addLayout(pattern_layout)
        
        # Movement pattern preview
        preview_layout = QHBoxLayout()
        preview_layout.addWidget(QLabel("Pattern Preview:"))
        
        self.editor.pattern_preview_label = QLabel("No pattern selected")
        self.editor.pattern_preview_label.setStyleSheet(
            "border: 1px solid #ccc; padding: 10px; background-color: #f9f9f9; min-height: 60px;"
        )
        preview_layout.addWidget(self.editor.pattern_preview_label)
        
        layout.addLayout(preview_layout)
        
        group.setLayout(layout)
        return group
    
    def _create_abilities_section(self) -> QGroupBox:
        """
        Create the abilities section.
        
        Returns:
            The abilities group box
        """
        group = QGroupBox("Piece Abilities")
        abilities_layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel(
            "Select abilities for this piece. Use the search box to filter abilities."
        )
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #666; font-style: italic; margin-bottom: 10px;")
        abilities_layout.addWidget(instructions)
        
        # Main abilities layout (horizontal split)
        main_abilities_layout = QHBoxLayout()
        
        # Search section
        search_section = QVBoxLayout()
        search_section.addWidget(QLabel("🔍 Search Abilities:"))
        
        self.editor.ability_search_edit = QLineEdit()
        self.editor.ability_search_edit.setPlaceholderText("Type to filter abilities...")
        self.editor.ability_search_edit.textChanged.connect(self.editor.filter_abilities)
        search_section.addWidget(self.editor.ability_search_edit)
        
        # Abilities scroll area
        abilities_scroll = QScrollArea()
        abilities_scroll.setWidgetResizable(True)
        abilities_scroll.setMaximumHeight(200)
        
        # Container widget for checkboxes
        abilities_container = QWidget()
        self.editor.abilities_checkboxes_layout = QVBoxLayout(abilities_container)
        self.editor.abilities_checkboxes_layout.setSpacing(2)
        
        # Dictionary to store checkbox references
        self.editor.ability_checkboxes = {}
        
        abilities_scroll.setWidget(abilities_container)
        
        # Add search section and abilities to main layout
        main_abilities_layout.addLayout(search_section, 1)
        main_abilities_layout.addWidget(abilities_scroll, 3)
        
        abilities_layout.addLayout(main_abilities_layout)
        
        # Quick action buttons
        ability_buttons_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("✅ Select All")
        select_all_btn.clicked.connect(self.editor.select_all_abilities)
        select_all_btn.setToolTip("Select all available abilities")
        ability_buttons_layout.addWidget(select_all_btn)
        
        clear_all_btn = QPushButton("❌ Clear All")
        clear_all_btn.clicked.connect(self.editor.clear_all_abilities)
        clear_all_btn.setToolTip("Deselect all abilities")
        ability_buttons_layout.addWidget(clear_all_btn)
        
        ability_buttons_layout.addStretch()
        
        # Ability Editor button
        open_editor_btn = QPushButton("🛠️ Ability Editor")
        open_editor_btn.clicked.connect(self.editor.open_ability_editor)
        open_editor_btn.setToolTip("Open Ability Editor to create or modify abilities")
        ability_buttons_layout.addWidget(open_editor_btn)
        
        abilities_layout.addLayout(ability_buttons_layout)
        
        group.setLayout(abilities_layout)
        return group
    
    def _create_promotion_section(self) -> QGroupBox:
        """
        Create the promotion section.
        
        Returns:
            The promotion group box
        """
        group = QGroupBox("Promotion Settings")
        promo_layout = QVBoxLayout()
        
        # Primary Promotions
        primary_promo_layout = QHBoxLayout()
        primary_promo_layout.addWidget(QLabel("Primary Promotions:"))
        primary_promo_info = QLabel("(When piece reaches enemy back rank)")
        primary_promo_info.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        primary_promo_layout.addWidget(primary_promo_info)
        
        self.editor.primary_promo_btn = QPushButton("Select Pieces...")
        self.editor.primary_promo_btn.clicked.connect(self.editor.promotion_manager.open_primary_promotion_selector)
        primary_promo_layout.addWidget(self.editor.primary_promo_btn)
        primary_promo_layout.addStretch()
        promo_layout.addLayout(primary_promo_layout)
        
        # Primary promotion display
        self.editor.primary_promo_display = QLabel("None selected")
        self.editor.primary_promo_display.setStyleSheet(
            "padding: 5px; background-color: palette(base); color: palette(text); "
            "border: 1px solid palette(mid); border-radius: 3px;"
        )
        promo_layout.addWidget(self.editor.primary_promo_display)
        
        # Secondary Promotions
        secondary_promo_layout = QHBoxLayout()
        secondary_promo_layout.addWidget(QLabel("Secondary Promotions:"))
        secondary_promo_info = QLabel("(A promoted piece gets a secondary promotion if it gets to its friendly back rank)")
        secondary_promo_info.setStyleSheet("color: #666; font-style: italic; font-size: 10px;")
        secondary_promo_layout.addWidget(secondary_promo_info)
        
        self.editor.secondary_promo_btn = QPushButton("Select Pieces...")
        self.editor.secondary_promo_btn.clicked.connect(self.editor.promotion_manager.open_secondary_promotion_selector)
        secondary_promo_layout.addWidget(self.editor.secondary_promo_btn)
        secondary_promo_layout.addStretch()
        promo_layout.addLayout(secondary_promo_layout)
        
        # Secondary promotion display
        self.editor.secondary_promo_display = QLabel("None selected")
        self.editor.secondary_promo_display.setStyleSheet(
            "padding: 5px; background-color: palette(base); color: palette(text); "
            "border: 1px solid palette(mid); border-radius: 3px;"
        )
        promo_layout.addWidget(self.editor.secondary_promo_display)
        
        group.setLayout(promo_layout)
        return group
    
    def _create_points_section(self) -> QGroupBox:
        """
        Create the points and recharge section.
        
        Returns:
            The points group box
        """
        group = QGroupBox("Points and Recharge")
        layout = QFormLayout()
        
        # Max points
        self.editor.max_points_spin = QSpinBox()
        self.editor.max_points_spin.setRange(0, 100)
        self.editor.max_points_spin.setValue(3)
        layout.addRow("Max Points:", self.editor.max_points_spin)
        
        # Starting points
        self.editor.starting_points_spin = QSpinBox()
        self.editor.starting_points_spin.setRange(0, 100)
        self.editor.starting_points_spin.setValue(2)
        layout.addRow("Starting Points:", self.editor.starting_points_spin)
        
        # Recharge type
        self.editor.recharge_type_combo = QComboBox()
        for recharge_type, _ in RECHARGE_TYPES:
            self.editor.recharge_type_combo.addItem(recharge_type)
        self.editor.recharge_type_combo.currentTextChanged.connect(self.editor.update_recharge_options)
        layout.addRow("Recharge Type:", self.editor.recharge_type_combo)
        
        # Recharge options (will be populated dynamically)
        self.editor.recharge_options_layout = QVBoxLayout()
        layout.addRow("Recharge Options:", self.editor.recharge_options_layout)
        
        group.setLayout(layout)
        return group
    
    def _create_icon_section(self) -> QGroupBox:
        """
        Create the icon section.

        Returns:
            The icon group box
        """
        group = QGroupBox("Piece Icons")
        layout = QVBoxLayout()

        # Icon selection will be handled by icon manager
        # The icon manager will create its UI directly in this layout
        self.editor.icon_manager.create_icon_ui(layout)

        group.setLayout(layout)
        return group
    
    def _create_console_section(self) -> QGroupBox:
        """
        Create the console log section.
        
        Returns:
            The console group box
        """
        group = QGroupBox("Console Log")
        layout = QVBoxLayout()
        
        self.editor.log_console = QTextEdit()
        self.editor.log_console.setMaximumHeight(100)
        self.editor.log_console.setReadOnly(True)
        self.editor.log_console.setStyleSheet(
            "background-color: #2b2b2b; color: #ffffff; font-family: 'Courier New', monospace; font-size: 10px;"
        )
        layout.addWidget(self.editor.log_console)
        
        group.setLayout(layout)
        return group
    
    def _get_movement_button_style(self) -> str:
        """
        Get the CSS style for movement pattern buttons.
        
        Returns:
            CSS style string
        """
        return """
            QPushButton {
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #555;
                border-radius: 6px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #3a3a3a, stop:1 #2a2a2a);
                color: white;
                padding: 2px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #4a4a4a, stop:1 #3a3a3a);
                border-color: #66aaff;
            }
            QPushButton:checked {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #4488cc, stop:1 #3366aa);
                border-color: #66aaff;
                border-width: 3px;
            }
        """
