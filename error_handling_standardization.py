#!/usr/bin/env python3
"""
Error Handling Standardization for Adventure Chess Creator

This module provides standardized error handling patterns and utilities
to ensure consistent error handling across the entire codebase.
"""

import logging
import traceback
from typing import Tuple, Optional, Any, Dict, List, Callable
from functools import wraps
from pathlib import Path

logger = logging.getLogger(__name__)

class StandardErrorHandler:
    """Standardized error handling utilities"""
    
    @staticmethod
    def safe_operation(operation_name: str) -> Callable:
        """
        Decorator for standardizing error handling in operations
        
        Returns: Tuple[bool, Optional[str]] - (success, error_message)
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Tuple[bool, Optional[str]]:
                try:
                    result = func(*args, **kwargs)
                    
                    # If function already returns (bool, str) tuple, pass through
                    if isinstance(result, tuple) and len(result) == 2:
                        return result
                    
                    # If function returns other value, wrap as success
                    return True, None
                    
                except Exception as e:
                    error_msg = f"Error in {operation_name}: {str(e)}"
                    logger.error(error_msg)
                    logger.debug(f"Full traceback for {operation_name}: {traceback.format_exc()}")
                    return False, error_msg
            
            return wrapper
        return decorator
    
    @staticmethod
    def safe_data_operation(operation_name: str, data_type: str = "data") -> Callable:
        """
        Decorator for data operations with enhanced error context
        
        Args:
            operation_name: Name of the operation (e.g., "save_piece", "load_ability")
            data_type: Type of data being operated on (e.g., "piece", "ability")
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Tuple[bool, Optional[str]]:
                try:
                    # Extract filename if available for better error context
                    filename = None
                    if args and hasattr(args[0], 'get'):
                        filename = args[0].get('name', 'unknown')
                    elif len(args) > 1 and isinstance(args[1], str):
                        filename = args[1]
                    
                    result = func(*args, **kwargs)
                    
                    # Standardize return format
                    if isinstance(result, tuple) and len(result) == 2:
                        success, error = result
                        if success:
                            logger.debug(f"Successfully completed {operation_name} for {data_type}: {filename}")
                        return success, error
                    
                    # Assume success if no tuple returned
                    logger.debug(f"Successfully completed {operation_name} for {data_type}: {filename}")
                    return True, None
                    
                except FileNotFoundError as e:
                    error_msg = f"File not found in {operation_name} for {data_type}: {str(e)}"
                    logger.error(error_msg)
                    return False, error_msg
                    
                except PermissionError as e:
                    error_msg = f"Permission denied in {operation_name} for {data_type}: {str(e)}"
                    logger.error(error_msg)
                    return False, error_msg
                    
                except ValueError as e:
                    error_msg = f"Invalid data in {operation_name} for {data_type}: {str(e)}"
                    logger.error(error_msg)
                    return False, error_msg
                    
                except Exception as e:
                    error_msg = f"Unexpected error in {operation_name} for {data_type}: {str(e)}"
                    logger.error(error_msg)
                    logger.debug(f"Full traceback for {operation_name}: {traceback.format_exc()}")
                    return False, error_msg
            
            return wrapper
        return decorator
    
    @staticmethod
    def safe_ui_operation(operation_name: str, widget_name: str = "widget") -> Callable:
        """
        Decorator for UI operations with user-friendly error handling
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs) -> Tuple[bool, Optional[str]]:
                try:
                    result = func(*args, **kwargs)
                    
                    if isinstance(result, tuple) and len(result) == 2:
                        return result
                    
                    return True, None
                    
                except AttributeError as e:
                    error_msg = f"UI component error in {operation_name} for {widget_name}: {str(e)}"
                    logger.error(error_msg)
                    return False, f"Interface error: {widget_name} component not available"
                    
                except Exception as e:
                    error_msg = f"UI error in {operation_name} for {widget_name}: {str(e)}"
                    logger.error(error_msg)
                    return False, f"Interface error in {widget_name}"
            
            return wrapper
        return decorator

class ErrorReporter:
    """Centralized error reporting and logging"""
    
    def __init__(self):
        self.error_counts: Dict[str, int] = {}
        self.recent_errors: List[Dict[str, Any]] = []
        self.max_recent_errors = 100
    
    def report_error(self, operation: str, error: str, context: Optional[Dict] = None):
        """Report an error with context"""
        # Count errors by operation
        self.error_counts[operation] = self.error_counts.get(operation, 0) + 1
        
        # Store recent error
        error_entry = {
            'operation': operation,
            'error': error,
            'context': context or {},
            'timestamp': logger.handlers[0].formatter.formatTime(logging.LogRecord(
                name='', level=0, pathname='', lineno=0, msg='', args=(), exc_info=None
            )) if logger.handlers else 'unknown'
        }
        
        self.recent_errors.append(error_entry)
        
        # Limit recent errors list
        if len(self.recent_errors) > self.max_recent_errors:
            self.recent_errors = self.recent_errors[-self.max_recent_errors:]
        
        # Log the error
        logger.error(f"[{operation}] {error}")
        if context:
            logger.debug(f"Error context: {context}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of errors"""
        return {
            'total_operations_with_errors': len(self.error_counts),
            'total_error_count': sum(self.error_counts.values()),
            'error_counts_by_operation': self.error_counts.copy(),
            'recent_errors_count': len(self.recent_errors),
            'most_common_errors': sorted(
                self.error_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
        }

# Global error reporter instance
error_reporter = ErrorReporter()

def standardize_error_handling_in_file(file_path: Path) -> Tuple[bool, Optional[str]]:
    """
    Analyze and suggest standardization improvements for a Python file
    """
    try:
        if not file_path.exists() or not file_path.suffix == '.py':
            return False, f"Invalid Python file: {file_path}"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        suggestions = []
        
        # Check for bare except clauses
        if 'except:' in content:
            suggestions.append("Replace bare 'except:' with specific exception types")
        
        # Check for inconsistent return patterns
        if 'return False' in content and 'return True' in content:
            if 'return False,' not in content or 'return True,' not in content:
                suggestions.append("Standardize return format to Tuple[bool, Optional[str]]")
        
        # Check for inconsistent logging
        if 'print(' in content and 'logger.' in content:
            suggestions.append("Replace print statements with logger calls")
        
        # Check for missing error context
        if 'except Exception as e:' in content:
            if f'Error in' not in content:
                suggestions.append("Add operation context to error messages")
        
        if suggestions:
            return True, f"Suggestions for {file_path.name}: " + "; ".join(suggestions)
        else:
            return True, f"Error handling in {file_path.name} follows standards"
            
    except Exception as e:
        return False, f"Error analyzing {file_path}: {str(e)}"

def apply_error_handling_standards():
    """Apply error handling standards across the codebase"""
    logger.info("Applying error handling standardization...")
    
    # Key files to standardize
    key_files = [
        "utils/simple_bridge.py",
        "utils/direct_data_manager.py", 
        "schemas/data_manager.py",
        "editors/base_editor.py"
    ]
    
    results = []
    
    for file_path in key_files:
        path = Path(file_path)
        success, message = standardize_error_handling_in_file(path)
        results.append({
            'file': file_path,
            'success': success,
            'message': message
        })
        
        if success:
            logger.info(message)
        else:
            logger.error(message)
    
    return results

if __name__ == "__main__":
    # Run error handling standardization analysis
    print("Analyzing error handling patterns...")
    results = apply_error_handling_standards()
    
    print("\nError Handling Standardization Results:")
    print("=" * 50)
    
    for result in results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['file']}")
        print(f"   {result['message']}")
    
    print(f"\nAnalyzed {len(results)} files")
    print("Error handling standardization analysis complete!")
