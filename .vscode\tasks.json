{"version": "2.0.0", "tasks": [{"label": "Adventure Chess: Run Tests", "type": "shell", "command": "python", "args": ["run_tests.py"], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Adventure Chess: <PERSON><PERSON><PERSON>", "type": "shell", "command": "python", "args": ["analyze_imports.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Adventure Chess: Analyze Code Duplication", "type": "shell", "command": "python", "args": ["analyze_code_duplication.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Adventure Chess: Launch Main App", "type": "shell", "command": "python", "args": ["main.py"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Adventure Chess: <PERSON> Cache", "type": "shell", "command": "powershell", "args": ["-Command", "Get-ChildItem -Path . -Recurse -Name '__pycache__' | Remove-Item -Recurse -Force; Get-ChildItem -Path . -Recurse -Name '*.pyc' | Remove-Item -Force; Write-Host '<PERSON><PERSON> cleaned successfully'"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}