2025-06-18 22:47:01,380 - utils - INFO - Logging initialized with level: INFO
2025-06-18 22:47:01,596 - piece_manager - INFO - Added version field to piece
2025-06-18 22:47:01,596 - piece_manager - INFO - Migrated movement format from 'Orthogonal' to dict format
2025-06-18 22:47:01,597 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-18 22:47:01,597 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Adventure Pawn.json
2025-06-18 22:47:01,600 - piece_manager - INFO - Added version field to piece
2025-06-18 22:47:01,600 - piece_manager - INFO - Migrated movement format from 'Diagonal' to dict format
2025-06-18 22:47:01,601 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-18 22:47:01,601 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Bishop.json
2025-06-18 22:47:01,604 - piece_manager - INFO - Added version field to piece
2025-06-18 22:47:01,605 - piece_manager - INFO - Migrated movement format from 'Any' to dict format
2025-06-18 22:47:01,605 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\King.json
2025-06-18 22:47:01,607 - piece_manager - INFO - Added version field to piece
2025-06-18 22:47:01,607 - piece_manager - INFO - Migrated movement format from 'L-shape' to dict format
2025-06-18 22:47:01,608 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Knight.json
2025-06-18 22:47:01,610 - piece_manager - INFO - Added version field to piece
2025-06-18 22:47:01,610 - piece_manager - INFO - Migrated movement format from 'Custom' to dict format
2025-06-18 22:47:01,611 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-18 22:47:01,611 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Pawn.json
2025-06-18 22:47:01,614 - piece_manager - INFO - Added version field to piece
2025-06-18 22:47:01,614 - piece_manager - INFO - Migrated movement format from 'Any' to dict format
2025-06-18 22:47:01,615 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Queen.json
2025-06-18 22:47:01,617 - piece_manager - INFO - Added version field to piece
2025-06-18 22:47:01,617 - piece_manager - INFO - Migrated movement format from 'Orthogonal' to dict format
2025-06-18 22:47:01,618 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Rook.json
2025-06-18 22:51:06,402 - utils - INFO - Logging initialized with level: INFO
2025-06-18 22:51:48,004 - ability_manager - INFO - Added version field to ability
2025-06-18 22:51:48,004 - ability_manager - INFO - Migrated boolean field 'capture' to tag 'capture'
2025-06-18 22:51:48,005 - ability_manager - INFO - Migrated boolean field 'range' to tag 'range'
2025-06-18 22:51:48,005 - ability_manager - INFO - Migrated ability schema in C:\Users\<USER>\Desktop\Adventure chess\data\abilities\Magic_Bolt_1_Range.json
2025-06-18 22:51:48,021 - ability_manager - INFO - Added version field to ability
2025-06-18 22:51:48,022 - ability_manager - INFO - Migrated boolean field 'capture' to tag 'capture'
2025-06-18 22:51:48,022 - ability_manager - INFO - Migrated boolean field 'range' to tag 'range'
2025-06-18 22:51:48,022 - ability_manager - INFO - Migrated ability schema in C:\Users\<USER>\Desktop\Adventure chess\data\abilities\Magic_Bolt_Front.json
2025-06-18 22:51:48,024 - ability_manager - INFO - Added version field to ability
2025-06-18 22:51:48,024 - ability_manager - INFO - Migrated boolean field 'summon' to tag 'summon'
2025-06-18 22:51:48,025 - ability_manager - INFO - Migrated boolean field 'range' to tag 'range'
2025-06-18 22:51:48,025 - ability_manager - INFO - Migrated ability schema in C:\Users\<USER>\Desktop\Adventure chess\data\abilities\summon_pawn.json
2025-06-18 22:51:48,027 - ability_manager - INFO - Added version field to ability
2025-06-18 22:51:48,028 - ability_manager - INFO - Migrated boolean field 'teleport' to tag 'teleport'
2025-06-18 22:51:48,028 - ability_manager - INFO - Migrated boolean field 'range' to tag 'range'
2025-06-18 22:51:48,028 - ability_manager - INFO - Migrated ability schema in C:\Users\<USER>\Desktop\Adventure chess\data\abilities\teleport_basic.json
2025-06-18 22:51:48,030 - ability_manager - WARNING - Ability not found: Magic_Bolt_1_Range
2025-06-18 22:51:48,045 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-18 22:51:48,057 - ability_manager - WARNING - Ability not found: summon_pawn
2025-06-18 22:51:48,072 - ability_manager - WARNING - Ability not found: teleport_basic
2025-06-18 22:51:56,373 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-18 22:51:58,420 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-18 22:52:21,559 - piece_manager - WARNING - Piece save warning: Piece missing version field, assuming legacy format
2025-06-18 22:52:21,560 - piece_manager - INFO - Saved piece 'Adventure Pawn' to C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Adventure Pawn.json
2025-06-18 22:52:46,371 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-18 22:53:58,505 - utils - INFO - Logging initialized with level: INFO
2025-06-18 22:53:58,572 - piece_manager - INFO - Migrated movement format from 'Custom' to dict format
2025-06-18 22:53:58,572 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-18 22:53:58,572 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess\data\pieces\Adventure Pawn.json
2025-06-18 23:00:26,216 - utils - INFO - Logging initialized with level: INFO
2025-06-18 23:04:22,301 - utils - INFO - Logging initialized with level: INFO
2025-06-18 23:06:40,649 - utils - INFO - Logging initialized with level: INFO
2025-06-18 23:14:46,226 - utils - INFO - Logging initialized with level: INFO
2025-06-18 23:22:54,563 - utils - INFO - Logging initialized with level: INFO
2025-06-19 08:29:56,062 - utils - INFO - Logging initialized with level: INFO
2025-06-19 08:54:01,553 - utils - INFO - Logging initialized with level: INFO
2025-06-19 09:23:18,121 - utils - INFO - Logging initialized with level: INFO
2025-06-19 10:13:32,614 - utils - INFO - Logging initialized with level: INFO
2025-06-19 10:55:30,712 - utils - INFO - Logging initialized with level: INFO
2025-06-19 10:58:15,570 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-19 10:58:15,572 - ability_manager - INFO - Saved ability 'Pawns Rook Mobilization' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Pawns Rook Mobilization.json
2025-06-19 11:19:52,128 - utils - INFO - Logging initialized with level: INFO
2025-06-19 11:32:59,644 - utils - INFO - Logging initialized with level: INFO
2025-06-19 11:52:50,313 - utils - INFO - Logging initialized with level: INFO
2025-06-19 12:39:49,571 - utils - INFO - Logging initialized with level: INFO
2025-06-19 12:59:56,549 - utils - INFO - Logging initialized with level: INFO
2025-06-19 13:00:47,807 - ability_manager - WARNING - Ability not found: Magic_Bolt_1_Range
2025-06-19 13:00:47,811 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 13:00:47,814 - ability_manager - WARNING - Ability not found: summon_pawn
2025-06-19 13:00:47,816 - ability_manager - WARNING - Ability not found: teleport_basic
2025-06-19 13:01:02,133 - ability_manager - WARNING - Ability not found: Magic_Bolt_1_Range
2025-06-19 13:01:02,134 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 13:01:02,136 - ability_manager - WARNING - Ability not found: summon_pawn
2025-06-19 13:01:02,138 - ability_manager - WARNING - Ability not found: teleport_basic
2025-06-19 13:01:04,816 - ability_manager - WARNING - Ability not found: Magic_Bolt_1_Range
2025-06-19 13:01:04,818 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 13:01:04,820 - ability_manager - WARNING - Ability not found: summon_pawn
2025-06-19 13:01:04,822 - ability_manager - WARNING - Ability not found: teleport_basic
2025-06-19 13:09:45,200 - utils - INFO - Logging initialized with level: INFO
2025-06-19 13:10:26,840 - piece_manager - WARNING - Piece save warning: Piece missing version field, assuming legacy format
2025-06-19 13:10:26,843 - piece_manager - INFO - Saved piece 'Adventure Pawn' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\pieces\Adventure Pawn.json
2025-06-19 13:11:28,092 - utils - INFO - Logging initialized with level: INFO
2025-06-19 13:11:28,178 - piece_manager - INFO - Migrated movement format from 'Orthogonal' to dict format
2025-06-19 13:11:28,178 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-19 13:11:28,179 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess - Copy\data\pieces\Adventure Pawn.json
2025-06-19 13:11:28,181 - piece_manager - INFO - Migrated movement format from 'Any' to dict format
2025-06-19 13:11:28,181 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-19 13:11:28,182 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess - Copy\data\pieces\King.json
2025-06-19 13:11:28,184 - piece_manager - INFO - Migrated movement format from 'Orthogonal' to dict format
2025-06-19 13:11:28,185 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-19 13:11:28,185 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess - Copy\data\pieces\Rook.json
2025-06-19 13:15:35,032 - utils - INFO - Logging initialized with level: INFO
2025-06-19 13:18:20,876 - utils - INFO - Logging initialized with level: INFO
2025-06-19 14:42:02,390 - utils - INFO - Logging initialized with level: INFO
2025-06-19 14:42:40,860 - ability_manager - WARNING - Ability not found: Magic_Bolt_1_Range
2025-06-19 14:42:40,864 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 14:42:40,867 - ability_manager - WARNING - Ability not found: summon_pawn
2025-06-19 14:42:40,868 - ability_manager - WARNING - Ability not found: teleport_basic
2025-06-19 14:43:46,226 - ability_manager - WARNING - Ability not found: Magic_Bolt_1_Range
2025-06-19 14:43:46,228 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 14:43:46,230 - ability_manager - WARNING - Ability not found: summon_pawn
2025-06-19 14:43:46,232 - ability_manager - WARNING - Ability not found: teleport_basic
2025-06-19 14:43:50,864 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 14:43:52,106 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 14:43:54,845 - piece_manager - WARNING - Piece save warning: Piece missing version field, assuming legacy format
2025-06-19 14:43:54,848 - piece_manager - INFO - Saved piece 'Adventure Pawn' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\pieces\Adventure Pawn.json
2025-06-19 14:44:04,981 - ability_manager - WARNING - Ability not found: Magic_Bolt_Front
2025-06-19 15:01:18,212 - utils - INFO - Logging initialized with level: INFO
2025-06-19 15:01:18,282 - piece_manager - INFO - Migrated movement format from 'Orthogonal' to dict format
2025-06-19 15:01:18,282 - piece_manager - INFO - Migrated recharge format to nested dict
2025-06-19 15:01:18,283 - piece_manager - INFO - Migrated piece schema in C:\Users\<USER>\Desktop\Adventure chess - Copy\data\pieces\Adventure Pawn.json
2025-06-19 18:20:22,467 - utils - INFO - Logging initialized with level: INFO
2025-06-19 18:46:37,095 - utils - INFO - Logging initialized with level: INFO
2025-06-19 22:22:47,596 - utils - INFO - Logging initialized with level: INFO
2025-06-19 23:12:38,072 - utils - INFO - Logging initialized with level: INFO
2025-06-20 00:19:42,911 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:19:42,919 - ability_manager - INFO - Saved ability 'Multi-Target Combo' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Multi_Target_Combo.json
2025-06-20 00:21:23,647 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:21:23,650 - ability_manager - INFO - Saved ability 'Castling left' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Multi_Target_Combo.json
2025-06-20 00:22:45,895 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:22:45,898 - ability_manager - INFO - Saved ability 'Castling left' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Multi_Target_Combo.json
2025-06-20 00:27:33,827 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:27:33,830 - ability_manager - INFO - Saved ability 'Castling right' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Multi_Target_Combo.json
2025-06-20 00:28:49,369 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:28:49,372 - ability_manager - INFO - Saved ability 'Castling right' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Multi_Target_Combo.json
2025-06-20 00:32:50,293 - utils - INFO - Logging initialized with level: INFO
2025-06-20 00:45:31,736 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:45:31,739 - ability_manager - INFO - Saved ability 'Jesters Gambit' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Jesters Gambit.json
2025-06-20 00:48:17,838 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:48:17,841 - ability_manager - INFO - Saved ability 'Queens Birthing' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Queens Birthing.json
2025-06-20 00:52:03,618 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:52:03,620 - ability_manager - INFO - Saved ability 'Pawns Rook Mobilization' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Pawns Rook Mobilization.json
2025-06-20 00:58:32,237 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 00:58:32,241 - ability_manager - INFO - Saved ability 'Bishops Resurection' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Bishops Resurection.json
2025-06-20 01:01:14,540 - ability_manager - WARNING - Ability save warning: Ability missing version field, assuming legacy format
2025-06-20 01:01:14,542 - ability_manager - INFO - Saved ability 'Rooks Catapult' to C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\Rooks Catapult.json
2025-06-20 10:11:50,669 - utils - INFO - Logging initialized with level: INFO
2025-06-20 10:16:39,538 - utils - INFO - Logging initialized with level: INFO
2025-06-20 10:18:22,642 - utils - INFO - Logging initialized with level: INFO
2025-06-20 10:18:52,404 - utils - INFO - Logging initialized with level: INFO
2025-06-20 10:33:53,264 - ability_manager - INFO - Deleted ability file: C:\Users\<USER>\Desktop\Adventure chess - Copy\data\abilities\lantern_vision.json
2025-06-20 10:48:13,755 - utils - INFO - Logging initialized with level: INFO
2025-06-20 11:17:29,582 - utils - INFO - Logging initialized with level: INFO
2025-06-20 11:18:03,294 - utils - INFO - Logging initialized with level: INFO
2025-06-20 11:32:52,272 - utils - INFO - Logging initialized with level: INFO
2025-06-20 11:47:39,689 - utils - INFO - Logging initialized with level: INFO
2025-06-20 11:54:52,348 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:06:11,361 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:06:57,576 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:11:49,502 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:20:16,882 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:23:09,806 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:30:22,358 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:38:16,695 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:40:54,599 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:41:24,915 - utils - INFO - Logging initialized with level: INFO
2025-06-20 12:42:14,476 - utils - INFO - Logging initialized with level: INFO
2025-06-20 13:04:05,512 - utils - INFO - Logging initialized with level: INFO
2025-06-20 13:34:32,024 - utils - INFO - Logging initialized with level: INFO
2025-06-20 13:49:41,389 - utils - INFO - Logging initialized with level: INFO
2025-06-20 14:27:17,024 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:01:08,274 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:06:43,828 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:12:27,123 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:16:50,479 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:20:04,222 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:20:41,824 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:21:30,656 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:23:24,107 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:38:39,434 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:39:58,835 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:42:21,795 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:46:07,251 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:50:36,106 - utils - INFO - Logging initialized with level: INFO
2025-06-20 15:51:20,618 - utils - INFO - Logging initialized with level: INFO
2025-06-20 20:52:31,710 - utils - INFO - Logging initialized with level: INFO
2025-06-20 20:54:06,493 - utils - INFO - Logging initialized with level: INFO
2025-06-20 21:20:10,544 - utils - INFO - Logging initialized with level: INFO
2025-06-20 21:30:46,616 - utils - INFO - Logging initialized with level: INFO
2025-06-20 21:39:56,433 - utils - INFO - Logging initialized with level: INFO
2025-06-20 21:53:39,827 - utils - INFO - Logging initialized with level: INFO
2025-06-20 21:58:31,077 - utils - INFO - Logging initialized with level: INFO
2025-06-20 22:53:35,324 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:07:26,789 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:12:39,272 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:14:50,779 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:18:12,162 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:21:30,799 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:22:07,457 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:38:49,962 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:39:42,609 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:42:06,175 - utils - INFO - Logging initialized with level: INFO
2025-06-20 23:42:37,313 - utils - INFO - Logging initialized with level: INFO
2025-06-21 00:01:03,820 - utils - INFO - Logging initialized with level: INFO
2025-06-21 00:01:43,038 - utils - INFO - Logging initialized with level: INFO
2025-06-21 00:07:40,175 - utils - INFO - Logging initialized with level: INFO
2025-06-21 00:14:52,765 - utils - INFO - Logging initialized with level: INFO
2025-06-21 00:40:01,346 - utils - INFO - Logging initialized with level: INFO
2025-06-21 01:01:10,626 - utils - INFO - Logging initialized with level: INFO
2025-06-21 01:05:07,336 - utils - INFO - Logging initialized with level: INFO
2025-06-21 09:07:01,334 - utils - INFO - Logging initialized with level: INFO
2025-06-21 10:54:48,536 - utils - INFO - Logging initialized with level: INFO
2025-06-21 11:10:21,872 - utils - INFO - Logging initialized with level: INFO
2025-06-21 11:11:45,665 - utils - INFO - Logging initialized with level: INFO
2025-06-21 11:22:43,582 - utils - INFO - Logging initialized with level: INFO
2025-06-21 11:27:35,475 - utils - INFO - Logging initialized with level: INFO
2025-06-21 11:38:01,374 - utils - INFO - Logging initialized with level: INFO
2025-06-21 11:54:12,111 - utils - INFO - Logging initialized with level: INFO
2025-06-21 12:02:41,501 - utils - INFO - Logging initialized with level: INFO
2025-06-21 12:04:11,592 - utils - INFO - Logging initialized with level: INFO
2025-06-21 12:15:39,709 - utils - INFO - Logging initialized with level: INFO
2025-06-21 12:20:46,627 - utils - INFO - Logging initialized with level: INFO
2025-06-21 13:35:32,690 - utils - INFO - Logging initialized with level: INFO
2025-06-21 13:44:18,299 - utils - INFO - Logging initialized with level: INFO
2025-06-21 13:48:36,893 - utils - INFO - Logging initialized with level: INFO
2025-06-21 14:00:43,792 - utils - INFO - Logging initialized with level: INFO
2025-06-21 14:04:07,834 - utils - INFO - Logging initialized with level: INFO
2025-06-21 14:15:36,671 - utils - INFO - Logging initialized with level: INFO
2025-06-21 14:21:35,014 - utils - INFO - Logging initialized with level: INFO
2025-06-21 14:27:56,999 - utils - INFO - Logging initialized with level: INFO
2025-06-21 15:24:52,219 - utils - INFO - Logging initialized with level: INFO
