#!/usr/bin/env python3
"""
Lazy Loading Integration for Adventure Chess Creator
Integrates lazy loading with existing data managers and UI components
"""

import os
import sys
import json
import logging
from typing import Dict, Any, Optional, List, Tuple, Callable
from pathlib import Path
from PyQt6.QtCore import QObject, pyqtSignal, QTimer
from PyQt6.QtWidgets import QComboBox, QListWidget, QListWidgetItem

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from lazy_loading_system import LazyDataManager, get_lazy_manager, LoadingProgressWidget
from enhanced_cache_manager import get_cache_manager

logger = logging.getLogger(__name__)

class LazyFileListManager(QObject):
    """
    Manages lazy loading for file lists in UI components like dropdowns and list widgets
    """
    
    files_loaded = pyqtSignal(list)  # Emitted when file metadata is loaded
    file_data_loaded = pyqtSignal(str, dict)  # Emitted when individual file data is loaded
    loading_progress = pyqtSignal(int, str)  # Progress updates
    
    def __init__(self, directory: str, file_extension: str = ".json", parent=None):
        super().__init__(parent)
        self.directory = directory
        self.file_extension = file_extension
        self.lazy_manager = get_lazy_manager(get_cache_manager())
        
        # File metadata cache
        self.file_metadata: List[Dict[str, Any]] = []
        self.metadata_loaded = False
        
        # UI components that need updates
        self.registered_combos: List[QComboBox] = []
        self.registered_lists: List[QListWidget] = []
        
    def register_combo_box(self, combo: QComboBox):
        """Register a combo box for lazy updates"""
        self.registered_combos.append(combo)
        if self.metadata_loaded:
            self._update_combo_box(combo)
    
    def register_list_widget(self, list_widget: QListWidget):
        """Register a list widget for lazy updates"""
        self.registered_lists.append(list_widget)
        if self.metadata_loaded:
            self._update_list_widget(list_widget)
    
    def load_file_list_lazy(self):
        """Load file list metadata without full file content"""
        if self.metadata_loaded:
            return
            
        def load_metadata():
            return self.lazy_manager.load_file_metadata_lazy(self.directory, self.file_extension)
        
        def on_metadata_loaded(metadata):
            self.file_metadata = metadata
            self.metadata_loaded = True
            self.files_loaded.emit(metadata)
            self._update_all_ui_components()
        
        # Load metadata in background
        self.lazy_manager.load_data_lazy(
            f"metadata_{self.directory}",
            load_metadata,
            on_metadata_loaded,
            priority=10  # High priority for metadata
        )
    
    def load_file_data_lazy(self, filename: str, priority: int = 0):
        """Load full file data lazily"""
        file_path = Path(self.directory) / f"{filename}{self.file_extension}"
        cache_key = str(file_path)
        
        def load_file():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading {file_path}: {e}")
                raise
        
        def on_file_loaded(data):
            self.file_data_loaded.emit(filename, data)
            self._update_file_in_ui(filename, data)
        
        self.lazy_manager.load_data_lazy(cache_key, load_file, on_file_loaded, priority)
    
    def _update_all_ui_components(self):
        """Update all registered UI components"""
        for combo in self.registered_combos:
            self._update_combo_box(combo)
        for list_widget in self.registered_lists:
            self._update_list_widget(list_widget)
    
    def _update_combo_box(self, combo: QComboBox):
        """Update combo box with file metadata"""
        combo.clear()
        combo.addItem("Select file to load...")
        
        for metadata in self.file_metadata:
            filename = metadata["filename"]
            display_name = metadata.get("display_name", filename)
            
            if metadata.get("has_error"):
                display_text = f"❌ {display_name} (Error)"
            elif metadata.get("is_loaded"):
                display_text = f"✅ {display_name}"
            else:
                display_text = f"📄 {display_name}"
            
            combo.addItem(display_text)
            combo.setItemData(combo.count() - 1, filename)
    
    def _update_list_widget(self, list_widget: QListWidget):
        """Update list widget with file metadata"""
        list_widget.clear()
        
        for metadata in self.file_metadata:
            filename = metadata["filename"]
            display_name = metadata.get("display_name", filename)
            
            if metadata.get("has_error"):
                item_text = f"❌ {display_name} (Error)"
                tooltip = f"Error: {metadata.get('error', 'Unknown error')}"
            elif metadata.get("is_loaded"):
                item_text = f"✅ {display_name}"
                tooltip = f"Loaded: {filename}\nDescription: {metadata.get('description', 'No description')}"
            else:
                item_text = f"📄 {display_name}"
                tooltip = f"File: {filename}\nSize: {metadata['size_bytes']} bytes\nModified: {metadata['modified_time']}"
            
            item = QListWidgetItem(item_text)
            item.setToolTip(tooltip)
            item.setData(0x0100, filename)  # UserRole
            list_widget.addItem(item)
    
    def _update_file_in_ui(self, filename: str, data: Dict[str, Any]):
        """Update UI components when file data is loaded"""
        # Update metadata
        for metadata in self.file_metadata:
            if metadata["filename"] == filename:
                metadata["is_loaded"] = True
                metadata["display_name"] = data.get("name", filename)
                metadata["description"] = data.get("description", "")
                break
        
        # Update UI components
        self._update_all_ui_components()

class LazyIntegratedDataManager:
    """
    Enhanced data manager that integrates lazy loading with caching
    Drop-in replacement for existing data managers with lazy loading capabilities
    """
    
    def __init__(self):
        self.cache_manager = get_cache_manager()
        self.lazy_manager = get_lazy_manager(self.cache_manager)
        
        # File list managers
        self.pieces_manager: Optional[LazyFileListManager] = None
        self.abilities_manager: Optional[LazyFileListManager] = None
        
        # Progress tracking
        self.loading_progress_callbacks: List[Callable[[int, str], None]] = []
    
    def get_pieces_manager(self) -> LazyFileListManager:
        """Get or create pieces file list manager"""
        if self.pieces_manager is None:
            from config import PIECES_DIR
            self.pieces_manager = LazyFileListManager(PIECES_DIR, ".json")
        return self.pieces_manager
    
    def get_abilities_manager(self) -> LazyFileListManager:
        """Get or create abilities file list manager"""
        if self.abilities_manager is None:
            from config import ABILITIES_DIR
            self.abilities_manager = LazyFileListManager(ABILITIES_DIR, ".json")
        return self.abilities_manager
    
    def load_piece_lazy(self, filename: str, callback: Optional[Callable[[Dict[str, Any]], None]] = None,
                       priority: int = 0) -> Optional[Dict[str, Any]]:
        """Load piece data lazily"""
        from config import PIECES_DIR, PIECE_EXTENSION
        
        # Normalize filename
        if not filename.endswith(PIECE_EXTENSION):
            filename += PIECE_EXTENSION
        
        file_path = Path(PIECES_DIR) / filename
        cache_key = str(file_path)
        
        def load_piece():
            if not file_path.exists():
                raise FileNotFoundError(f"Piece file not found: {filename}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return self.lazy_manager.load_data_lazy(cache_key, load_piece, callback, priority)
    
    def load_ability_lazy(self, filename: str, callback: Optional[Callable[[Dict[str, Any]], None]] = None,
                         priority: int = 0) -> Optional[Dict[str, Any]]:
        """Load ability data lazily"""
        from config import ABILITIES_DIR, ABILITY_EXTENSION
        
        # Normalize filename
        if not filename.endswith(ABILITY_EXTENSION):
            filename += ABILITY_EXTENSION
        
        file_path = Path(ABILITIES_DIR) / filename
        cache_key = str(file_path)
        
        def load_ability():
            if not file_path.exists():
                raise FileNotFoundError(f"Ability file not found: {filename}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return self.lazy_manager.load_data_lazy(cache_key, load_ability, callback, priority)
    
    def preload_recent_files(self, max_files: int = 10):
        """Preload recently modified files in background"""
        try:
            from config import PIECES_DIR, ABILITIES_DIR
            
            # Get recent pieces
            pieces_metadata = self.lazy_manager.load_file_metadata_lazy(PIECES_DIR)
            recent_pieces = pieces_metadata[:max_files]
            
            # Get recent abilities  
            abilities_metadata = self.lazy_manager.load_file_metadata_lazy(ABILITIES_DIR)
            recent_abilities = abilities_metadata[:max_files]
            
            # Create load functions
            load_functions = {}
            
            for metadata in recent_pieces:
                filename = metadata["filename"]
                load_functions[filename] = lambda f=filename: self._load_piece_sync(f)
            
            for metadata in recent_abilities:
                filename = metadata["filename"]
                load_functions[filename] = lambda f=filename: self._load_ability_sync(f)
            
            # Start preloading
            all_files = [m["filename"] for m in recent_pieces + recent_abilities]
            
            def progress_callback(progress, message):
                for callback in self.loading_progress_callbacks:
                    callback(progress, message)
            
            self.lazy_manager.preload_files(all_files, load_functions, progress_callback)
            
        except Exception as e:
            logger.error(f"Error preloading recent files: {e}")
    
    def _load_piece_sync(self, filename: str) -> Dict[str, Any]:
        """Synchronous piece loading for preloading"""
        from config import PIECES_DIR, PIECE_EXTENSION
        
        if not filename.endswith(PIECE_EXTENSION):
            filename += PIECE_EXTENSION
        
        file_path = Path(PIECES_DIR) / filename
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_ability_sync(self, filename: str) -> Dict[str, Any]:
        """Synchronous ability loading for preloading"""
        from config import ABILITIES_DIR, ABILITY_EXTENSION
        
        if not filename.endswith(ABILITY_EXTENSION):
            filename += ABILITY_EXTENSION
        
        file_path = Path(ABILITIES_DIR) / filename
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def add_progress_callback(self, callback: Callable[[int, str], None]):
        """Add a progress callback for loading operations"""
        self.loading_progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[int, str], None]):
        """Remove a progress callback"""
        if callback in self.loading_progress_callbacks:
            self.loading_progress_callbacks.remove(callback)
    
    def get_loading_status(self) -> Dict[str, Any]:
        """Get current loading status"""
        return self.lazy_manager.get_loading_status()
    
    def is_loading(self, filename: str) -> bool:
        """Check if a file is currently being loaded"""
        return self.lazy_manager.is_loading(filename)
    
    # Backward compatibility methods
    def load_piece(self, filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Backward compatible piece loading (synchronous)"""
        try:
            data = self._load_piece_sync(filename)
            return data, None
        except Exception as e:
            return None, str(e)
    
    def load_ability(self, filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Backward compatible ability loading (synchronous)"""
        try:
            data = self._load_ability_sync(filename)
            return data, None
        except Exception as e:
            return None, str(e)
    
    def save_piece(self, data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save piece data (delegates to cache manager)"""
        return self.cache_manager.save_piece(data, filename)
    
    def save_ability(self, data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Save ability data (delegates to cache manager)"""
        return self.cache_manager.save_ability(data, filename)
    
    def shutdown(self):
        """Shutdown the lazy data manager"""
        self.lazy_manager.shutdown()

# Global lazy integrated data manager
_lazy_data_manager: Optional[LazyIntegratedDataManager] = None

def get_lazy_data_manager() -> LazyIntegratedDataManager:
    """Get or create the global lazy integrated data manager"""
    global _lazy_data_manager
    if _lazy_data_manager is None:
        _lazy_data_manager = LazyIntegratedDataManager()
    return _lazy_data_manager
