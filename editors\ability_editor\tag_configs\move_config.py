"""
Move tag configuration for ability editor.
Handles movement-based ability configurations.
"""

from PyQt6.QtWidgets import QSpinBox, QCheckBox, QComboBox
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class MoveConfig(BaseTagConfig):
    """Configuration for move tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "move")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for move configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting move UI creation")
            
            # Create form layout
            form_layout = self.create_form_layout()
            self.log_debug("Created form layout")
            
            # Move distance spinner (1-8)
            move_distance = QSpinBox()
            move_distance.setRange(1, 8)
            move_distance.setValue(1)
            move_distance.setToolTip("Maximum movement distance")
            self.store_widget("move_distance", move_distance)
            self.connect_change_signals(move_distance)
            form_layout.addRow("Move Distance:", move_distance)
            self.log_debug("Added move distance spinner")
            
            # Move type dropdown
            move_type = QComboBox()
            move_type.addItems([
                "normal",      # standard movement
                "teleport",    # instant teleportation
                "jump",        # can jump over pieces
                "slide",       # must slide in straight line
                "knight"       # L-shaped movement
            ])
            move_type.setToolTip("Type of movement")
            self.store_widget("move_type", move_type)
            self.connect_change_signals(move_type)
            form_layout.addRow("Move Type:", move_type)
            self.log_debug("Added move type dropdown")
            
            # Move pattern dropdown
            move_pattern = QComboBox()
            move_pattern.addItems([
                "orthogonal",  # + shape
                "diagonal",    # x shape
                "all",         # both + and x
                "knight",      # L-shaped
                "custom"       # custom pattern
            ])
            move_pattern.setToolTip("Movement pattern")
            self.store_widget("move_pattern", move_pattern)
            self.connect_change_signals(move_pattern)
            form_layout.addRow("Move Pattern:", move_pattern)
            self.log_debug("Added move pattern dropdown")
            
            # Can move through pieces checkbox
            move_through = QCheckBox("Can move through pieces")
            move_through.setToolTip("Whether the piece can move through other pieces")
            self.store_widget("move_through_pieces", move_through)
            self.connect_change_signals(move_through)
            form_layout.addRow("", move_through)
            self.log_debug("Added move through pieces checkbox")
            
            # Must move full distance checkbox
            must_move_full = QCheckBox("Must move full distance")
            must_move_full.setToolTip("Whether the piece must move the full distance")
            self.store_widget("move_must_full", must_move_full)
            self.connect_change_signals(must_move_full)
            form_layout.addRow("", must_move_full)
            self.log_debug("Added must move full distance checkbox")
            
            # Can move to occupied squares checkbox
            move_to_occupied = QCheckBox("Can move to occupied squares")
            move_to_occupied.setToolTip("Whether the piece can move to squares occupied by other pieces")
            self.store_widget("move_to_occupied", move_to_occupied)
            self.connect_change_signals(move_to_occupied)
            form_layout.addRow("", move_to_occupied)
            self.log_debug("Added move to occupied checkbox")
            
            # Add to parent layout
            parent_layout.addLayout(form_layout)
            self.log_debug("Move UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting move data population with: {data}")
            
            # Populate move distance
            move_distance = self.get_widget_by_name("move_distance")
            if move_distance:
                distance_value = data.get("moveDistance", 1)
                self.log_debug(f"Setting move distance to: {distance_value}")
                move_distance.setValue(distance_value)
            
            # Populate move type
            move_type = self.get_widget_by_name("move_type")
            if move_type:
                type_value = data.get("moveType", "normal")
                self.log_debug(f"Setting move type to: {type_value}")
                index = move_type.findText(type_value)
                if index >= 0:
                    move_type.setCurrentIndex(index)
            
            # Populate move pattern
            move_pattern = self.get_widget_by_name("move_pattern")
            if move_pattern:
                pattern_value = data.get("movePattern", "orthogonal")
                self.log_debug(f"Setting move pattern to: {pattern_value}")
                index = move_pattern.findText(pattern_value)
                if index >= 0:
                    move_pattern.setCurrentIndex(index)
            
            # Populate move through pieces
            move_through = self.get_widget_by_name("move_through_pieces")
            if move_through:
                through_value = data.get("moveThroughPieces", False)
                self.log_debug(f"Setting move through pieces to: {through_value}")
                move_through.setChecked(through_value)
            
            # Populate must move full
            must_move_full = self.get_widget_by_name("move_must_full")
            if must_move_full:
                full_value = data.get("moveMustFull", False)
                self.log_debug(f"Setting must move full to: {full_value}")
                must_move_full.setChecked(full_value)
            
            # Populate move to occupied
            move_to_occupied = self.get_widget_by_name("move_to_occupied")
            if move_to_occupied:
                occupied_value = data.get("moveToOccupied", False)
                self.log_debug(f"Setting move to occupied to: {occupied_value}")
                move_to_occupied.setChecked(occupied_value)
            
            self.log_debug("Move data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the move configuration data
        """
        try:
            data = {}
            
            # Collect move distance
            move_distance = self.get_widget_by_name("move_distance")
            if move_distance:
                data["moveDistance"] = move_distance.value()
            
            # Collect move type
            move_type = self.get_widget_by_name("move_type")
            if move_type:
                data["moveType"] = move_type.currentText()
            
            # Collect move pattern
            move_pattern = self.get_widget_by_name("move_pattern")
            if move_pattern:
                data["movePattern"] = move_pattern.currentText()
            
            # Collect move through pieces
            move_through = self.get_widget_by_name("move_through_pieces")
            if move_through:
                data["moveThroughPieces"] = move_through.isChecked()
            
            # Collect must move full
            must_move_full = self.get_widget_by_name("move_must_full")
            if must_move_full:
                data["moveMustFull"] = must_move_full.isChecked()
            
            # Collect move to occupied
            move_to_occupied = self.get_widget_by_name("move_to_occupied")
            if move_to_occupied:
                data["moveToOccupied"] = move_to_occupied.isChecked()
            
            self.log_debug(f"Collected move data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
