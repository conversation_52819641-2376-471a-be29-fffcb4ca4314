{"timestamp": "2025-06-25T14:25:52.220833", "summary": {"pieces": {"total": 6, "valid": 6, "invalid": 0, "missing_version": 0, "corrupted": 0}, "abilities": {"total": 10, "valid": 10, "invalid": 0, "missing_version": 0, "corrupted": 0}, "temp_files": 0, "missing_references": 0}, "details": {"pieces": {"valid": [{"file": "data\\pieces\\Adventure Bishop.json", "type": "piece", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Adventure Bishop", "description": "Mystical bishop with revival powers", "role": "Commander", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "Diagonal", "distance": 8}, "recharge": {"type": "None", "turns": 0}, "abilities": ["Bishops revival", "Bishops Teleport home"], "promotions": []}}, {"file": "data\\pieces\\Adventure King.json", "type": "piece", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Adventure King", "description": "Royal king with swapping abilities", "role": "King", "can_castle": true, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "Any", "distance": 1}, "recharge": {"type": "None", "turns": 0}, "abilities": ["Kings Swap"], "promotions": []}}, {"file": "data\\pieces\\Adventure Knight.json", "type": "piece", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Adventure Knight", "description": "Agile knight with carrying abilities", "role": "Commander", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "L-shape", "distance": 1}, "recharge": {"type": "None", "turns": 0}, "abilities": ["Knights Carry"], "promotions": []}}, {"file": "data\\pieces\\Adventure Pawn.json", "type": "piece", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Adventure Pawn", "description": "Basic pawn with enhanced abilities", "role": "Soldier", "can_castle": false, "track_starting_position": true, "color_directional": true, "can_capture": true, "movement": {"type": "Custom", "pattern": [[false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, true, false, false, false], [false, false, false, true, true, true, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false]], "piece_position": [4, 4]}, "recharge": {"type": "None", "turns": 0}, "abilities": ["Pawns Magic Bolt"], "promotions": ["Adventure Queen", "Adventure Rook", "Adventure Bishop", "Adventure Knight"]}}, {"file": "data\\pieces\\Adventure Queen.json", "type": "piece", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Adventure Queen", "description": "Powerful queen with summoning abilities", "role": "Commander", "can_castle": false, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "Any", "distance": 8}, "recharge": {"type": "None", "turns": 0}, "abilities": ["Queens sumon"], "promotions": []}}, {"file": "data\\pieces\\Adventure Rook.json", "type": "piece", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Adventure Rook", "description": "Powerful rook with special abilities", "role": "Commander", "can_castle": true, "track_starting_position": false, "color_directional": false, "can_capture": true, "movement": {"type": "Orthogonal", "distance": 8}, "recharge": {"type": "None", "turns": 0}, "abilities": [], "promotions": []}}], "invalid": [], "missing_version": [], "corrupted": []}, "abilities": {"valid": [{"file": "data\\abilities\\Bishops revival.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Bishops revival", "description": "Can revive pieces within line of sight", "cost": 1, "tags": ["revival", "range", "losRequired"], "activationMode": "click", "rangeMask": [[true, false, false, false, false, false, false, false], [false, true, false, false, false, false, false, true], [false, false, true, false, false, false, true, false], [false, false, false, true, false, true, false, false], [false, false, false, false, false, false, false, false], [false, false, false, true, false, true, false, false], [false, false, true, false, false, false, true, false], [false, true, false, false, false, false, false, true]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "revivalSacrifice": true, "revivalMaxCost": 3, "revivalWithPoints": true, "revivalPoints": 1, "revivalStarting": true, "revivalWithinTurn": 1, "revivalList": [{"piece": "Friendly Adventure Pawn", "cost": 1}, {"piece": "Friendly Adventure Bishop", "cost": 2}, {"piece": "Friendly Adventure King", "cost": 1}, {"piece": "Friendly Adventure Knight", "cost": 2}, {"piece": "Friendly Adventure Queen", "cost": 3}, {"piece": "Friendly Adventure Rook", "cost": 2}], "losIgnoreEnemy": false, "losIgnoreAll": true}}, {"file": "data\\abilities\\Bishops Teleport home.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Bishops Teleport home", "description": "Teleports bishop to its home square", "tags": ["move", "range"], "_dialogStates": {"piece_selector": {"piece_selection": "Any", "cost_value": 1, "no_cost_checked": false}, "ability_selector": {"selected_abilities": [], "cost_value": 1, "no_cost_checked": false, "search_text": ""}}, "autoCost": false, "displaceCustomMap": null, "_editorDialogStates": {"piece_selector": {"piece_selection": "Any", "cost_value": 1, "no_cost_checked": false}, "ability_selector": {"selected_abilities": [], "cost_value": 1, "no_cost_checked": false, "search_text": ""}, "range_editor_checkboxes": {"starting_square_checked": true, "continue_off_board_checked": false}, "pattern_editor_checkboxes": {"starting_square_checked": false, "continue_off_board_checked": false}}, "activationMode": "click", "rangeFriendlyOnly": false, "rangeEnemyOnly": false}}, {"file": "data\\abilities\\Kings Swap.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Kings Swap", "description": "Can swap place with any friendly pawn.", "cost": 0, "tags": ["swapPlaces", "range"], "autoCost": false, "activationMode": "click", "rangeMask": [[true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true], [true, true, true, true, false, true, true, true], [true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true], [true, true, true, true, true, true, true, true]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "swapList": [{"piece": "Friendly Adventure Pawn", "cost": 1}]}}, {"file": "data\\abilities\\Knights Carry.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Knights Carry", "description": "Can Carry Pawns across the map", "cost": 0, "tags": ["carryP<PERSON>ce"], "activationMode": "click", "carryRange": 0, "carryShareAbilities": true, "carryStartingPiece": false, "carryList": [{"piece": "Friendly Adventure Pawn", "cost": 0}]}}, {"file": "data\\abilities\\Magic_Bolt_1_Range.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Magic Bolt 1 Range", "description": "Magical projectile attack with 1 tile range", "cost": 1, "tags": ["capture", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false], [false, false, false, true, false, true, false, false], [false, false, false, true, true, true, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "captureTarget": "Enemy"}}, {"file": "data\\abilities\\Magic_Bolt_Front.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Magic Bolt Front", "description": "Magical projectile that attacks the square directly in front", "cost": 1, "tags": ["capture", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, true, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "captureTarget": "Enemy"}}, {"file": "data\\abilities\\Pawns Magic Bolt.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Pawns Magic Bolt", "description": "Can shoot the square directly in front of the pawn", "cost": 1, "tags": ["capture", "range"], "rangeCheckboxStates": {"starting_square_checked": false, "continue_off_board_checked": false}, "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, true, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false, "captureTarget": "Enemy"}}, {"file": "data\\abilities\\Queens sumon.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Queens sumon", "description": " Can summon 1 pawn withen 1 range on her side of the map", "cost": 2, "tags": ["summon", "range"], "_dialogStates": {"piece_selector": {"piece_selection": "Any", "cost_value": 1, "no_cost_checked": false}, "ability_selector": {"selected_abilities": [], "cost_value": 1, "no_cost_checked": false, "search_text": ""}}, "autoCost": true, "individualCosts": ["Summon: Friendly Adventure Pawn (1)", "Range Pattern: 8 tiles (1)"], "autoCalculateCost": true, "displaceCustomMap": null, "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false], [false, false, false, true, false, true, false, false], [false, false, false, true, true, true, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": true, "rangeEnemyOnly": false, "summonMax": 1, "summonList": [{"piece": "Friendly Adventure Pawn", "cost": 1}]}}, {"file": "data\\abilities\\summon_pawn.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "<PERSON><PERSON><PERSON> Pawn", "description": "<PERSON><PERSON>on a friendly pawn on an adjacent square", "cost": 2, "tags": ["summon", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false], [false, false, false, true, false, true, false, false], [false, false, false, true, true, true, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": true, "rangeEnemyOnly": false, "summonMax": 1, "summonList": [{"piece": "Friendly Adventure Pawn", "cost": 1}]}}, {"file": "data\\abilities\\teleport_basic.json", "type": "ability", "status": "valid", "issues": [], "data": {"version": "1.0.0", "name": "Teleport Basic", "description": "Teleport to any empty square within 3 tiles", "cost": 2, "tags": ["move", "range"], "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false], [false, true, true, true, true, true, true, true], [false, true, true, true, true, true, true, true], [false, true, true, true, true, true, true, true], [false, true, true, true, false, true, true, true], [false, true, true, true, true, true, true, true], [false, true, true, true, true, true, true, true], [false, true, true, true, true, true, true, true]], "piecePosition": [4, 4], "rangeFriendlyOnly": false, "rangeEnemyOnly": false}}], "invalid": [], "missing_version": [], "corrupted": []}, "temp_files": [], "missing_references": []}}