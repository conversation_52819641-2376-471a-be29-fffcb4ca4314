"""
Duplicate tag configuration for ability editor.
Handles duplicate ability configurations with position editing.
"""

from PyQt6.QtWidgets import (QFormLayout, QPushButton, QWidget, QLabel, QVBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class DuplicateConfig(BaseTagConfig):
    """Configuration for duplicate tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "duplicate")
        # Initialize duplicate positions map
        self.duplicate_positions_map = [[False for _ in range(8)] for _ in range(8)]
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for duplicate configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting duplicate UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Creates a copy of the activating piece.")
            layout.addWidget(description)

            # Form layout for options
            form_layout = QFormLayout()

            # Location offset button (reuses existing grid editor concept)
            duplicate_offset_btn = QPushButton("Edit Duplicate Positions")
            duplicate_offset_btn.setToolTip("Define where duplicates can be placed")
            self.store_widget("duplicate_offset_btn", duplicate_offset_btn)
            form_layout.addRow("Positions:", duplicate_offset_btn)

            layout.addLayout(form_layout)
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Duplicate UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating duplicate data")

            # Store duplicate positions map
            if "duplicatePositionsMap" in data:
                self.duplicate_positions_map = data["duplicatePositionsMap"]

            self.log_debug("Duplicate data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting duplicate data")
            data = {}

            # Collect duplicate positions map
            if self.duplicate_positions_map:
                data["duplicatePositionsMap"] = self.duplicate_positions_map

            self.log_debug("Duplicate data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
