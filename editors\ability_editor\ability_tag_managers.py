"""
Ability Tag Managers for Adventure Chess Creator

This module handles all tag-related functionality for the ability editor:
- Tag checkbox creation and management
- Dynamic configuration UI generation based on selected tags
- Tag-specific data cleanup and validation
- Configuration section creation for each tag type

Extracted from ability_editor.py to improve maintainability and make
tag management more modular and easier to extend.
"""

import logging
from typing import Dict, List, Any, Optional, Set, Callable
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QCheckBox,
    QPushButton, QSizePolicy, QSpinBox, QFormLayout
)
from PyQt6.QtCore import Qt

# Local imports
from config import ABILITY_TAGS
from ui.ui_utils import ResponsiveLayout
from .tag_configs import get_tag_config, get_available_tags

logger = logging.getLogger(__name__)


class AbilityTagManager:
    """
    Manages ability tags and their associated configuration UI.
    
    This class handles:
    - Creating tag checkboxes organized by category
    - Dynamically updating configuration UI based on selected tags
    - Managing tag-specific data and cleanup
    - Providing extensible tag configuration system
    """
    
    def __init__(self, editor_instance):
        """
        Initialize the tag manager.
        
        Args:
            editor_instance: The AbilityEditorWindow instance
        """
        self.editor = editor_instance
        self.tag_groups = {}  # Store tag checkbox references
        self.active_tag_configs = {}  # Store active tag configuration instances
        self.tag_config_methods = self._initialize_tag_config_map()
    
    def _initialize_tag_config_map(self) -> Dict[str, tuple]:
        """
        Initialize the mapping of tags to their configuration methods.
        
        Returns:
            Dictionary mapping tag names to (method, title) tuples
        """
        return {
            "range": (self._add_range_config, "🎯 Range Configuration"),
            "areaEffect": (self._add_area_effect_config, "💥 Area Effect Configuration"),
            "move": (self._add_move_config, "🚶 Move Configuration"),
            "summon": (self._add_summon_config, "✨ Summon Configuration"),
            "revival": (self._add_revival_config, "⚰️ Revival Configuration"),
            "capture": (self._add_capture_config, "⚔️ Capture Configuration"),
            "carryPiece": (self._add_carry_piece_config, "🎒 Carry Piece Configuration"),
            "swapPlaces": (self._add_swap_places_config, "🔄 Swap Places Configuration"),
            "displacePiece": (self._add_displacement_config, "🌪️ Displacement Configuration"),
            "adjacencyRequired": (self._add_adjacency_config, "🔗 Adjacency Configuration"),
            "noTurnCost": (self._add_no_turn_cost_config, "⚡ No Turn Cost Configuration"),
            "shareSpace": (self._add_share_space_config, "👥 Share Space Configuration"),
            "reaction": (self._add_reaction_config, "⚡ Reaction Configuration"),
            "vision": (self._add_vision_config, "👁️ Vision Configuration"),
            "obstacle": (self._add_obstacle_config, "🚧 Obstacle Configuration"),
            "buffPiece": (self._add_buff_piece_config, "⬆️ Buff Piece Configuration"),
            "debuffPiece": (self._add_debuff_piece_config, "⬇️ Debuff Piece Configuration"),
            "passThrough": (self._add_pass_through_config, "🚶 Pass Through Configuration"),
            "losRequired": (self._add_los_required_config, "👁️ Line of Sight Required Configuration"),
            "delay": (self._add_delay_config, "⏰ Delay Configuration"),
            "immobilize": (self._add_immobilize_config, "🔒 Immobilize Configuration"),
            "pulseEffect": (self._add_pulse_effect_config, "📡 Pulse Effect Configuration"),
            "fogOfWar": (self._add_fog_of_war_config, "🌫️ Fog of War Configuration"),
            "addObstacle": (self._add_add_obstacle_config, "🧱 Add Obstacle Configuration"),
            "removeObstacle": (self._add_remove_obstacle_config, "🗑️ Remove Obstacle Configuration"),
            "duplicate": (self._add_duplicate_config, "👥 Duplicate Configuration"),
            "convertPiece": (self._add_convert_piece_config, "🔄 Convert Piece Configuration"),
            "invisible": (self._add_invisible_config, "👻 Invisible Configuration"),
            "trapTile": (self._add_trap_tile_config, "🪤 Trap Tile Configuration"),
            "requiresStartingPosition": (self._add_requires_starting_position_config, "🏠 Starting Position Requirement"),
        }
    
    def create_tags_ui(self, parent_layout: QVBoxLayout) -> None:
        """
        Create the tag selection UI with organized groups.
        
        Args:
            parent_layout: The layout to add tag groups to
        """
        try:
            # Add instructions at the top
            instructions = QLabel(
                "💡 Tip: Click checkboxes to select/unselect ability tags. "
                "Selected tags will show configuration options in the Configuration tab."
            )
            instructions.setWordWrap(True)
            instructions.setStyleSheet("""
                QLabel {
                    background-color: #e8f4fd;
                    color: #2c3e50;
                    padding: 10px;
                    border: 1px solid #bee5eb;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
            """)
            parent_layout.addWidget(instructions)
            
            # Create tag groups from centralized config
            tag_groups_config = [
                ("🟢 Action Tags", ABILITY_TAGS['action']),
                ("🟡 Targeting Tags", ABILITY_TAGS['targeting']),
                ("🔵 Condition Tags", ABILITY_TAGS['condition']),
                ("🔶 Special Tags", ABILITY_TAGS['special'])
            ]
            
            for group_name, tags in tag_groups_config:
                self._create_tag_group(parent_layout, group_name, tags)
            
            logger.info("Tag UI created successfully")
            
        except Exception as e:
            logger.error(f"Error creating tags UI: {e}")
            raise
    
    def _create_tag_group(self, parent_layout: QVBoxLayout, group_name: str, tags: List[tuple]) -> None:
        """
        Create a group of tag checkboxes.
        
        Args:
            parent_layout: The parent layout to add the group to
            group_name: Name of the tag group
            tags: List of (tag_name, tooltip) tuples
        """
        group = QGroupBox(group_name)
        group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        group_layout = ResponsiveLayout.create_grid(margin=5, spacing=5)
        
        for i, (tag, tooltip) in enumerate(tags):
            cb = QCheckBox(tag)
            cb.setToolTip(tooltip)
            cb.stateChanged.connect(self.on_tag_changed)
            
            # Store reference for easy access
            self.tag_groups[tag] = cb
            
            # Add to grid layout (2 columns)
            row = i // 2
            col = i % 2
            group_layout.addWidget(cb, row, col)
        
        group.setLayout(group_layout)
        parent_layout.addWidget(group)
    
    def on_tag_changed(self) -> None:
        """Handle tag checkbox changes - update configuration tab."""
        try:
            self.update_configuration_ui()
            
            # Update auto cost if enabled
            if (hasattr(self.editor, 'auto_cost_check') and 
                self.editor.auto_cost_check.isChecked()):
                if hasattr(self.editor, 'calculate_auto_cost'):
                    self.editor.calculate_auto_cost()
            
            # Mark as unsaved changes
            if hasattr(self.editor, 'mark_unsaved_changes'):
                self.editor.mark_unsaved_changes()
            
        except Exception as e:
            logger.error(f"Error handling tag change: {e}")
    
    def update_configuration_ui(self) -> None:
        """Update the configuration UI based on selected tags."""
        try:
            # Clear existing configuration widgets using the new cleanup method
            self.cleanup_configuration_ui()
            
            # Get selected tags
            selected_tags = self.get_selected_tags()
            
            if not selected_tags:
                self._show_no_tags_message()
                return
            
            # Add configuration sections for each selected tag
            for tag in selected_tags:
                self.add_config_for_tag(tag)
            
            # Add stretch to push content to top
            self.editor.config_content_layout.addStretch()
            
            logger.debug(f"Configuration UI updated for tags: {selected_tags}")
            
        except Exception as e:
            logger.error(f"Error updating configuration UI: {e}")
    
    def _show_no_tags_message(self) -> None:
        """Show message when no tags are selected."""
        no_tags_label = QLabel("Select ability tags to see configuration options.")
        no_tags_label.setStyleSheet("color: #999; font-style: italic; padding: 20px;")
        no_tags_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.editor.config_content_layout.addWidget(no_tags_label)
    
    def get_selected_tags(self) -> Set[str]:
        """
        Get the set of currently selected tags.

        Returns:
            Set of selected tag names
        """
        selected_tags = set()
        for tag, checkbox in self.tag_groups.items():
            if checkbox.isChecked():
                selected_tags.add(tag)
        return selected_tags

    def set_selected_tags(self, tags: List[str]) -> None:
        """
        Set the selected tags from a list (used when loading data).

        Args:
            tags: List of tag names to select
        """
        try:
            # First, uncheck all tags
            for checkbox in self.tag_groups.values():
                checkbox.setChecked(False)

            # Then check the specified tags
            for tag in tags:
                if tag in self.tag_groups:
                    self.tag_groups[tag].setChecked(True)
                else:
                    logger.warning(f"Unknown tag in data: {tag}")

            # Update configuration UI after setting tags
            self.update_configuration_ui()

            logger.debug(f"Set selected tags: {tags}")

        except Exception as e:
            logger.error(f"Error setting selected tags: {e}")
    
    def add_config_for_tag(self, tag: str) -> None:
        """
        Add configuration UI for a specific tag.
        
        Args:
            tag: The tag name to add configuration for
        """
        try:
            if tag in self.tag_config_methods:
                config_method, title = self.tag_config_methods[tag]
                self._add_config_section_with_uncheck_button(tag, title, config_method)
            else:
                logger.warning(f"No configuration method found for tag: {tag}")
                
        except Exception as e:
            logger.error(f"Error adding config for tag {tag}: {e}")
    
    def _add_config_section_with_uncheck_button(self, tag: str, title: str, config_method: Callable) -> None:
        """
        Add a configuration section with an uncheck button in the title.
        
        Args:
            tag: The tag name
            title: The section title
            config_method: The method to call for creating the configuration UI
        """
        try:
            # Create a custom widget for the group box title with uncheck button
            title_widget = QWidget()
            title_layout = QHBoxLayout()
            title_layout.setContentsMargins(0, 0, 0, 0)
            
            # Uncheck button on the left
            uncheck_btn = QPushButton("❌")
            uncheck_btn.setFixedSize(20, 20)
            uncheck_btn.setToolTip(f"Remove {tag} tag")
            uncheck_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffebee;
                    border: 1px solid #ffcdd2;
                    border-radius: 10px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #ffcdd2;
                }
            """)
            uncheck_btn.clicked.connect(lambda: self.uncheck_ability_tag(tag))
            title_layout.addWidget(uncheck_btn)
            
            # Title label
            title_label = QLabel(title)
            title_label.setStyleSheet("font-weight: bold; margin-left: 5px;")
            title_layout.addWidget(title_label)
            
            title_layout.addStretch()
            title_widget.setLayout(title_layout)
            
            # Create the configuration group
            group = QGroupBox()
            group.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
            
            # Set the custom title widget
            group.setTitle("")  # Clear default title
            group_layout = ResponsiveLayout.create_vbox()
            group_layout.addWidget(title_widget)
            
            # Store the current group layout for modular configs to use
            self._current_config_layout = group_layout

            # Add the actual configuration content
            config_method()  # This should add widgets to the group

            # Clear the temporary layout reference
            self._current_config_layout = None
            
            group.setLayout(group_layout)
            self.editor.config_content_layout.addWidget(group)
            
        except Exception as e:
            logger.error(f"Error creating config section for {tag}: {e}")
    
    def uncheck_ability_tag(self, tag: str) -> None:
        """
        Uncheck a specific ability tag and clean up related data.
        
        Args:
            tag: The tag name to uncheck
        """
        try:
            if tag in self.tag_groups:
                self.tag_groups[tag].setChecked(False)
                self.clean_tag_data(tag)
                # This will trigger on_tag_changed which updates the configuration UI
                logger.info(f"Unchecked tag: {tag}")
                
        except Exception as e:
            logger.error(f"Error unchecking tag {tag}: {e}")
    
    def clean_tag_data(self, tag: str) -> None:
        """
        Clean up data related to a specific tag when it's unchecked.
        
        Args:
            tag: The tag name to clean up data for
        """
        try:
            # Tag-specific data cleanup mappings
            tag_cleanup_mappings = {
                'carryPiece': {
                    'lists': ['carry_pieces', 'carry_targets'],
                    'attributes': ['carry_range_pattern', 'carry_range_piece_position',
                                  'carry_drop_range_pattern', 'carry_drop_range_piece_position']
                },
                'summon': {
                    'lists': ['summon_pieces'],
                    'attributes': ['summon_range_pattern', 'summon_range_piece_position']
                },
                'revival': {
                    'lists': ['revival_pieces'],
                    'attributes': ['revival_range_pattern', 'revival_range_piece_position']
                },
                # Add more tag cleanup mappings as needed
            }
            
            if tag in tag_cleanup_mappings:
                cleanup_config = tag_cleanup_mappings[tag]
                
                # Clear list attributes
                for list_attr in cleanup_config.get('lists', []):
                    if hasattr(self.editor, list_attr):
                        getattr(self.editor, list_attr).clear()
                
                # Reset other attributes
                for attr in cleanup_config.get('attributes', []):
                    if hasattr(self.editor, attr):
                        setattr(self.editor, attr, None)
            
            logger.debug(f"Cleaned up data for tag: {tag}")
            
        except Exception as e:
            logger.error(f"Error cleaning tag data for {tag}: {e}")
    
    # Placeholder methods for tag configuration - these will be implemented
    # in the main editor or separate configuration modules
    
    def _add_range_config(self):
        """Add range configuration using modular system."""
        self._add_modular_tag_config("range")

    def _add_area_effect_config(self):
        """Add area effect configuration using modular system."""
        self._add_modular_tag_config("areaEffect")

    def _add_move_config(self):
        """Add move configuration using modular system."""
        self._add_modular_tag_config("move")
    def _add_summon_config(self):
        """Add summon configuration using modular system."""
        self._add_modular_tag_config("summon")

    def _add_revival_config(self):
        """Add revival configuration using modular system."""
        self._add_modular_tag_config("revival")
    def _add_capture_config(self):
        """Add capture configuration using modular system."""
        self._add_modular_tag_config("capture")
    def _add_modular_tag_config(self, tag_name: str, parent_layout=None):
        """
        Add tag configuration using the modular system.

        Args:
            tag_name: The name of the tag to configure
            parent_layout: The layout to add the configuration to (optional)
        """
        try:
            logger.debug(f"Attempting to add modular config for tag: {tag_name}")

            # Get the tag configuration class
            config_class = get_tag_config(tag_name)
            logger.debug(f"Got config class for {tag_name}: {config_class}")

            if not config_class:
                logger.warning(f"No modular configuration found for tag: {tag_name}")
                return

            # Create and store the configuration instance
            config_instance = config_class(self.editor)
            self.active_tag_configs[tag_name] = config_instance
            logger.debug(f"Created config instance for {tag_name}")

            # Create the UI
            target_layout = (parent_layout or
                           getattr(self, '_current_config_layout', None) or
                           getattr(self.editor, 'config_content_layout', None))
            if target_layout:
                logger.debug(f"Creating UI for {tag_name}")
                config_instance.create_ui(target_layout)

                # Populate with existing data if available
                if hasattr(self.editor, 'current_data') and self.editor.current_data:
                    logger.debug(f"Populating data for {tag_name}: {self.editor.current_data}")
                    config_instance.populate_data(self.editor.current_data)
                else:
                    logger.debug(f"No current_data available for {tag_name}")
            else:
                logger.warning(f"No config_content_layout found for {tag_name}")

            logger.info(f"Successfully added modular configuration for tag: {tag_name}")

        except Exception as e:
            logger.error(f"Error adding modular configuration for {tag_name}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    def _add_carry_piece_config(self):
        """Add carry piece configuration using modular system."""
        self._add_modular_tag_config("carryPiece")

    def _add_swap_places_config(self):
        """Add swap places configuration using modular system."""
        self._add_modular_tag_config("swapPlaces")

    def _add_displacement_config(self):
        """Add displacement configuration using modular system."""
        self._add_modular_tag_config("displacePiece")

    def _add_adjacency_config(self):
        """Add adjacency configuration using modular system."""
        self._add_modular_tag_config("adjacencyRequired")

    def _add_no_turn_cost_config(self):
        """Add no turn cost configuration using modular system."""
        self._add_modular_tag_config("noTurnCost")

    def _add_share_space_config(self):
        """Add share space configuration - placeholder."""
        pass

    def _add_reaction_config(self):
        """Add reaction configuration - placeholder."""
        pass

    def _add_vision_config(self):
        """Add vision configuration - placeholder."""
        pass

    def _add_obstacle_config(self):
        """Add obstacle configuration - placeholder."""
        pass

    def _add_buff_piece_config(self):
        """Add buff piece configuration - placeholder."""
        pass

    def _add_debuff_piece_config(self):
        """Add debuff piece configuration - placeholder."""
        pass

    def _add_pass_through_config(self):
        """Add pass through configuration - placeholder."""
        pass

    def _add_los_required_config(self):
        """Add line of sight required configuration - placeholder."""
        pass

    def _add_delay_config(self):
        """Add delay configuration - placeholder."""
        pass

    def _add_immobilize_config(self):
        """Add immobilize configuration - placeholder."""
        pass

    def _add_pulse_effect_config(self):
        """Add pulse effect configuration - placeholder."""
        pass

    def _add_fog_of_war_config(self):
        """Add fog of war configuration - placeholder."""
        pass

    def _add_add_obstacle_config(self):
        """Add add obstacle configuration - placeholder."""
        pass

    def _add_remove_obstacle_config(self):
        """Add remove obstacle configuration - placeholder."""
        pass

    def _add_duplicate_config(self):
        """Add duplicate configuration - placeholder."""
        pass

    def _add_convert_piece_config(self):
        """Add convert piece configuration - placeholder."""
        pass

    def _add_invisible_config(self):
        """Add invisible configuration - placeholder."""
        pass

    def _add_trap_tile_config(self):
        """Add trap tile configuration - placeholder."""
        pass

    def _add_requires_starting_position_config(self):
        """Add requires starting position configuration - placeholder."""
        pass

    def cleanup_configuration_ui(self):
        """
        Clean up the configuration UI by removing all widgets.
        This is called when tags are deselected or changed.
        """
        try:
            # Clear active tag configurations
            self.active_tag_configs.clear()

            if hasattr(self.editor, 'config_content_layout'):
                # Remove all widgets from the layout
                while self.editor.config_content_layout.count():
                    child = self.editor.config_content_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

                logger.debug("Configuration UI cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up configuration UI: {e}")

    def collect_tag_data(self) -> Dict[str, Any]:
        """
        Collect data from all active tag configurations.

        Returns:
            Dictionary containing all tag configuration data
        """
        try:
            all_data = {}

            for tag_name, config_instance in self.active_tag_configs.items():
                try:
                    tag_data = config_instance.collect_data()
                    all_data.update(tag_data)
                    logger.debug(f"Collected data for tag {tag_name}: {tag_data}")
                except Exception as e:
                    logger.error(f"Error collecting data for tag {tag_name}: {e}")

            return all_data

        except Exception as e:
            logger.error(f"Error collecting tag data: {e}")
            return {}

    def populate_tag_data(self, data: Dict[str, Any]) -> None:
        """
        Populate all active tag configurations with data.

        Args:
            data: Dictionary containing ability data
        """
        try:
            logger.debug(f"populate_tag_data called with {len(self.active_tag_configs)} active configs")
            logger.debug(f"Data keys: {list(data.keys())}")

            for tag_name, config_instance in self.active_tag_configs.items():
                try:
                    logger.debug(f"Populating data for tag: {tag_name}")
                    config_instance.populate_data(data)
                    logger.info(f"Successfully populated data for tag: {tag_name}")
                except Exception as e:
                    logger.error(f"Error populating data for tag {tag_name}: {e}")
                    import traceback
                    logger.error(f"Traceback: {traceback.format_exc()}")

        except Exception as e:
            logger.error(f"Error populating tag data: {e}")
