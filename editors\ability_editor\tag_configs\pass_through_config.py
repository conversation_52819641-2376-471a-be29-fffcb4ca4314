"""
PassThrough tag configuration for ability editor.
Handles pass through ability configurations with piece selection and range patterns.
"""

from PyQt6.QtWidgets import (QFormLayout, QVBoxLayout, QHBoxLayout, QGroupBox,
                            QPushButton, QLabel, QWidget)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector


class PassThroughConfig(BaseTagConfig):
    """Configuration for passThrough tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "passThrough")
        # Initialize pass through pieces list
        self.pass_through_pieces = []
        self.pass_through_range_pattern = None
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for pass through configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting pass through UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Info label matching old editor
            info_label = QLabel("Move through another unit's square. Use adjacency settings to define which pieces allow pass-through.")
            info_label.setWordWrap(True)
            info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
            layout.addWidget(info_label)

            # Enhanced inline piece selector for pass through pieces
            pass_through_selector = InlinePieceSelector(self.editor, "Pass Through Pieces", allow_costs=True)
            self.store_widget("pass_through_selector", pass_through_selector)
            layout.addWidget(pass_through_selector)

            # Pass through range configuration
            range_group = QGroupBox("Pass Through Range")
            range_layout = QVBoxLayout()

            range_info = QLabel("Define custom range pattern for pass through targeting:")
            range_info.setStyleSheet("color: #666; font-style: italic;")
            range_layout.addWidget(range_info)

            range_btn_layout = QHBoxLayout()
            pass_through_range_btn = QPushButton("Edit Pass Through Range")
            pass_through_range_btn.setToolTip("Define custom range pattern for pass through targeting")
            self.store_widget("pass_through_range_btn", pass_through_range_btn)
            range_btn_layout.addWidget(pass_through_range_btn)
            range_btn_layout.addStretch()

            range_layout.addLayout(range_btn_layout)
            range_group.setLayout(range_layout)
            layout.addWidget(range_group)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Pass through UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating pass through data")

            # Populate pass through pieces
            pass_through_selector = self.get_widget_by_name("pass_through_selector")
            if pass_through_selector and "passThroughPieces" in data:
                pass_through_selector.set_pieces(data["passThroughPieces"])

            # Store range pattern data
            if "passThroughRangePattern" in data:
                self.pass_through_range_pattern = data["passThroughRangePattern"]

            self.log_debug("Pass through data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting pass through data")
            data = {}

            # Collect pass through pieces
            pass_through_selector = self.get_widget_by_name("pass_through_selector")
            if pass_through_selector:
                pieces = pass_through_selector.get_pieces()
                if pieces:
                    data["passThroughPieces"] = pieces

            # Collect range pattern
            if self.pass_through_range_pattern:
                data["passThroughRangePattern"] = self.pass_through_range_pattern

            self.log_debug("Pass through data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
