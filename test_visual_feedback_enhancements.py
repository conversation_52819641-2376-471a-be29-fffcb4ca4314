#!/usr/bin/env python3
"""
Test Suite for Visual Feedback Enhancements

This test suite validates all visual feedback enhancement components:
- Enhanced loading indicators
- Real-time validation widgets
- Enhanced grid visualization
- Operation feedback system
- Status bar enhancements
- Integration with existing editors
"""

import sys
import os
import unittest
import logging
from unittest.mock import Mock, MagicMock, patch
from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLineEdit, QSpinBox
from PyQt6.QtCore import QTimer
from PyQt6.QtTest import QTest

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from visual_feedback_enhancements import (
        EnhancedLoadingIndicator,
        RealTimeValidationWidget,
        EnhancedGridVisualization,
        OperationFeedbackManager,
        StatusBarEnhancement,
        ColorSchemes
    )
    from visual_feedback_integration import (
        VisualFeedbackManager,
        integrate_visual_feedback_into_piece_editor,
        integrate_visual_feedback_into_ability_editor,
        get_visual_feedback_manager
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure visual feedback files are in the same directory")
    sys.exit(1)

class TestEnhancedLoadingIndicator(unittest.TestCase):
    """Test enhanced loading indicator functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Setup test application"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Setup test case"""
        self.indicator = EnhancedLoadingIndicator()
    
    def test_loading_indicator_creation(self):
        """Test loading indicator creation"""
        self.assertIsNotNone(self.indicator)
        self.assertFalse(self.indicator.is_loading)
        self.assertFalse(self.indicator.isVisible())
    
    def test_show_loading(self):
        """Test showing loading state"""
        self.indicator.show_loading("Test loading", "Loading details")
        
        self.assertTrue(self.indicator.is_loading)
        self.assertTrue(self.indicator.isVisible())
        self.assertEqual(self.indicator.status_label.text(), "Test loading")
        self.assertEqual(self.indicator.details_label.text(), "Loading details")
    
    def test_update_progress(self):
        """Test progress updates"""
        self.indicator.show_loading("Test loading")
        self.indicator.update_progress(50, "Half done", "50% complete")
        
        self.assertEqual(self.indicator.progress_bar.value(), 50)
        self.assertEqual(self.indicator.status_label.text(), "Half done")
        self.assertEqual(self.indicator.details_label.text(), "50% complete")
    
    def test_hide_loading(self):
        """Test hiding loading state"""
        self.indicator.show_loading("Test loading")
        self.indicator.hide_loading("Success!")

        # Wait for fade animation to complete
        QTest.qWait(1600)  # Wait for success message display + fade

        self.assertFalse(self.indicator.is_loading)
        self.assertEqual(self.indicator.status_label.text(), "Success!")
        self.assertEqual(self.indicator.progress_bar.value(), 100)

class TestRealTimeValidationWidget(unittest.TestCase):
    """Test real-time validation widget functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Setup test application"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Setup test case"""
        self.validation_widget = RealTimeValidationWidget()
    
    def test_validation_widget_creation(self):
        """Test validation widget creation"""
        self.assertIsNotNone(self.validation_widget)
        self.assertEqual(len(self.validation_widget.validation_rules), 0)
        self.assertEqual(self.validation_widget.status_message.text(), "Ready for validation")
    
    def test_add_validation_rule(self):
        """Test adding validation rules"""
        def test_validator(data):
            return True, "Valid"
        
        self.validation_widget.add_validation_rule("Test Rule", test_validator)
        self.assertEqual(len(self.validation_widget.validation_rules), 1)
        self.assertEqual(self.validation_widget.validation_rules[0][0], "Test Rule")
    
    def test_validation_success(self):
        """Test successful validation"""
        def always_valid(data):
            return True, "Always valid"
        
        self.validation_widget.add_validation_rule("Always Valid", always_valid)
        self.validation_widget.update_data({"test": "data"})
        
        # Wait for validation timer
        QTest.qWait(600)
        
        self.assertEqual(self.validation_widget.status_icon.text(), "✓")
        self.assertEqual(self.validation_widget.status_message.text(), "All validations passed")
    
    def test_validation_failure(self):
        """Test failed validation"""
        def always_invalid(data):
            return False, "Always invalid"
        
        self.validation_widget.add_validation_rule("Always Invalid", always_invalid)
        self.validation_widget.update_data({"test": "data"})
        
        # Wait for validation timer
        QTest.qWait(600)
        
        self.assertEqual(self.validation_widget.status_icon.text(), "✗")
        self.assertIn("validation(s) failed", self.validation_widget.status_message.text())

class TestEnhancedGridVisualization(unittest.TestCase):
    """Test enhanced grid visualization functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Setup test application"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Setup test case"""
        self.grid = EnhancedGridVisualization(4, 4)
    
    def test_grid_creation(self):
        """Test grid creation"""
        self.assertIsNotNone(self.grid)
        self.assertEqual(self.grid.rows, 4)
        self.assertEqual(self.grid.cols, 4)
        self.assertEqual(len(self.grid.buttons), 4)
        self.assertEqual(len(self.grid.buttons[0]), 4)
    
    def test_cell_click(self):
        """Test cell clicking"""
        # Initial state should be 0
        self.assertEqual(self.grid.grid_data[0][0], 0)
        
        # Click cell to cycle through values
        self.grid.cell_clicked(0, 0)
        self.assertEqual(self.grid.grid_data[0][0], 1)
        
        # Click again to cycle
        self.grid.cell_clicked(0, 0)
        self.assertEqual(self.grid.grid_data[0][0], 2)
    
    def test_set_grid_data(self):
        """Test setting grid data"""
        test_data = [[1, 2], [3, 4]]
        self.grid.set_grid_data(test_data)
        
        self.assertEqual(self.grid.grid_data[0][0], 1)
        self.assertEqual(self.grid.grid_data[0][1], 2)
        self.assertEqual(self.grid.grid_data[1][0], 3)
        self.assertEqual(self.grid.grid_data[1][1], 4)

class TestOperationFeedbackManager(unittest.TestCase):
    """Test operation feedback manager functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Setup test application"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Setup test case"""
        self.manager = OperationFeedbackManager()
    
    def test_operation_lifecycle(self):
        """Test complete operation lifecycle"""
        operation_id = "test_operation"
        description = "Test operation"
        
        # Start operation
        self.manager.start_operation(operation_id, description)
        self.assertIn(operation_id, self.manager.active_operations)
        
        # Update operation
        self.manager.update_operation(operation_id, 50, "Half done")
        
        # Complete operation
        self.manager.complete_operation(operation_id, True, "Success")
        self.assertNotIn(operation_id, self.manager.active_operations)
    
    def test_feedback_widget_registration(self):
        """Test feedback widget registration"""
        mock_widget = Mock()
        operation_id = "test_operation"
        
        self.manager.register_feedback_widget(operation_id, mock_widget)
        self.assertIn(operation_id, self.manager.feedback_widgets)
        self.assertEqual(self.manager.feedback_widgets[operation_id], mock_widget)

class TestVisualFeedbackIntegration(unittest.TestCase):
    """Test visual feedback integration functionality"""
    
    @classmethod
    def setUpClass(cls):
        """Setup test application"""
        if not QApplication.instance():
            cls.app = QApplication(sys.argv)
        else:
            cls.app = QApplication.instance()
    
    def setUp(self):
        """Setup test case"""
        self.manager = VisualFeedbackManager()
        
        # Create mock editors
        self.mock_piece_editor = self._create_mock_piece_editor()
        self.mock_ability_editor = self._create_mock_ability_editor()
    
    def _create_mock_piece_editor(self):
        """Create mock piece editor"""
        editor = QWidget()
        editor.setLayout(QVBoxLayout())
        
        # Add mock fields
        editor.name_input = QLineEdit()
        editor.value_input = QSpinBox()
        editor.layout().addWidget(editor.name_input)
        editor.layout().addWidget(editor.value_input)
        
        # Mock methods
        editor.collect_data = Mock(return_value={'name': 'Test Piece', 'value': 5})
        
        return editor
    
    def _create_mock_ability_editor(self):
        """Create mock ability editor"""
        editor = QWidget()
        editor.setLayout(QVBoxLayout())
        
        # Add mock fields
        editor.name_input = QLineEdit()
        editor.cost_input = QSpinBox()
        editor.layout().addWidget(editor.name_input)
        editor.layout().addWidget(editor.cost_input)
        
        # Mock methods
        editor.collect_data = Mock(return_value={'name': 'Test Ability', 'cost': 3})
        
        return editor
    
    def test_piece_editor_enhancement(self):
        """Test piece editor enhancement"""
        self.manager.enhance_piece_editor(self.mock_piece_editor)
        
        # Check that enhancements were applied
        self.assertTrue(hasattr(self.mock_piece_editor, 'loading_indicator'))
        self.assertTrue(hasattr(self.mock_piece_editor, 'validation_widget'))
        self.assertTrue(hasattr(self.mock_piece_editor, 'operation_manager'))
        self.assertIn('piece_editor', self.manager.enhanced_editors)
    
    def test_ability_editor_enhancement(self):
        """Test ability editor enhancement"""
        self.manager.enhance_ability_editor(self.mock_ability_editor)
        
        # Check that enhancements were applied
        self.assertTrue(hasattr(self.mock_ability_editor, 'loading_indicator'))
        self.assertTrue(hasattr(self.mock_ability_editor, 'validation_widget'))
        self.assertTrue(hasattr(self.mock_ability_editor, 'operation_manager'))
        self.assertIn('ability_editor', self.manager.enhanced_editors)
    
    def test_operation_management(self):
        """Test operation management through integration"""
        self.manager.enhance_piece_editor(self.mock_piece_editor)
        
        # Test operation lifecycle
        self.manager.start_operation('piece_editor', 'test_op', 'Test operation')
        self.manager.update_operation('piece_editor', 'test_op', 50, 'Half done')
        self.manager.complete_operation('piece_editor', 'test_op', True, 'Success')
        
        # Should not raise exceptions

class TestColorSchemes(unittest.TestCase):
    """Test color scheme definitions"""
    
    def test_color_scheme_completeness(self):
        """Test that all required colors are defined"""
        required_colors = [
            'SUCCESS', 'ERROR', 'WARNING', 'INFO', 'LOADING',
            'GRID_EMPTY', 'GRID_MOVE', 'GRID_ATTACK', 'GRID_BOTH',
            'GRID_ACTION', 'GRID_ANY', 'GRID_TARGET', 'GRID_BORDER',
            'VALID', 'INVALID', 'PENDING',
            'DARK_BG', 'LIGHT_BG', 'CARD_BG', 'HOVER_BG'
        ]
        
        for color in required_colors:
            self.assertTrue(hasattr(ColorSchemes, color), f"Missing color: {color}")
            color_value = getattr(ColorSchemes, color)
            self.assertTrue(color_value.startswith('#'), f"Invalid color format: {color_value}")

def run_visual_feedback_tests():
    """Run all visual feedback enhancement tests"""
    print("Starting Visual Feedback Enhancement Tests...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_cases = [
        TestEnhancedLoadingIndicator,
        TestRealTimeValidationWidget,
        TestEnhancedGridVisualization,
        TestOperationFeedbackManager,
        TestVisualFeedbackIntegration,
        TestColorSchemes
    ]
    
    for test_case in test_cases:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_case)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print results
    if result.wasSuccessful():
        print("\n🎉 All Visual Feedback Enhancement Tests Passed! 🎉")
        print("\nVisual feedback enhancement system is ready for use!")
        return True
    else:
        print(f"\n❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        for test, traceback in result.failures + result.errors:
            print(f"FAILED: {test}")
            print(traceback)
        return False

if __name__ == "__main__":
    success = run_visual_feedback_tests()
    sys.exit(0 if success else 1)
