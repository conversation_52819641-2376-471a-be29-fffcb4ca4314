"""
Shared UI Components for Adventure Chess
Common UI patterns and utilities used across multiple modules
"""

# Standard library imports
from typing import List, Tuple, Optional, Callable

# PyQt6 imports
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, QLabel
)
from PyQt6.QtCore import Qt


class GridToggleWidget(QWidget):
    """
    Reusable grid widget with toggle functionality
    Used for pattern editors, area effect masks, etc.
    """
    
    def __init__(self, rows: int = 8, cols: int = 8, parent=None):
        super().__init__(parent)
        self.rows = rows
        self.cols = cols
        self.grid = [[0 for _ in range(cols)] for _ in range(rows)]
        self.buttons = []
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the grid UI"""
        layout = QVBoxLayout()
        
        # Grid
        grid_layout = QGridLayout()
        self.buttons = []
        
        for row in range(self.rows):
            button_row = []
            for col in range(self.cols):
                btn = QPushButton()
                btn.setFixedSize(30, 30)
                btn.setCheckable(True)
                btn.clicked.connect(lambda checked, r=row, c=col: self.toggle_tile(r, c))
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: #f0f0f0;
                        border: 1px solid #ccc;
                    }
                    QPushButton:checked {
                        background-color: #4caf50;
                        color: white;
                    }
                """)
                grid_layout.addWidget(btn, row, col)
                button_row.append(btn)
            self.buttons.append(button_row)
        
        layout.addLayout(grid_layout)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        clear_btn = QPushButton("Clear All")
        clear_btn.clicked.connect(self.clear_all)
        button_layout.addWidget(clear_btn)
        
        select_all_btn = QPushButton("Select All")
        select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(select_all_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def toggle_tile(self, row: int, col: int):
        """Toggle a tile in the grid"""
        if 0 <= row < self.rows and 0 <= col < self.cols:
            self.grid[row][col] = 1 - self.grid[row][col]
            self.buttons[row][col].setChecked(self.grid[row][col] == 1)
            self.update_display()
    
    def clear_all(self):
        """Clear all tiles"""
        for row in range(self.rows):
            for col in range(self.cols):
                self.grid[row][col] = 0
                self.buttons[row][col].setChecked(False)
        self.update_display()
    
    def select_all(self):
        """Select all tiles"""
        for row in range(self.rows):
            for col in range(self.cols):
                self.grid[row][col] = 1
                self.buttons[row][col].setChecked(True)
        self.update_display()
    
    def update_display(self):
        """Update the display - override in subclasses"""
        pass
    
    def get_mask(self) -> List[List[int]]:
        """Get the current grid mask"""
        return [row[:] for row in self.grid]  # Deep copy
    
    def set_mask(self, mask: List[List[int]]):
        """Set the grid mask"""
        if len(mask) == self.rows and all(len(row) == self.cols for row in mask):
            for row in range(self.rows):
                for col in range(self.cols):
                    self.grid[row][col] = mask[row][col]
                    self.buttons[row][col].setChecked(mask[row][col] == 1)
            self.update_display()


class AreaEffectGridWidget(GridToggleWidget):
    """
    Specialized grid widget for area effect masks
    Supports target position and boolean mask
    """
    
    def __init__(self, initial_mask=None, target_pos=None, parent=None):
        super().__init__(8, 8, parent)
        self.target_pos = target_pos or [3, 3]
        
        # Convert boolean mask to integer grid
        if initial_mask:
            for row in range(8):
                for col in range(8):
                    self.grid[row][col] = 1 if initial_mask[row][col] else 0
                    self.buttons[row][col].setChecked(initial_mask[row][col])
        
        self.update_display()
    
    def setup_ui(self):
        """Setup the grid UI with right-click support"""
        super().setup_ui()
        
        # Add right-click support for target position
        for row in range(self.rows):
            for col in range(self.cols):
                btn = self.buttons[row][col]
                btn.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                btn.customContextMenuRequested.connect(
                    lambda pos, r=row, c=col: self.set_target_position(r, c)
                )
    
    def set_target_position(self, row: int, col: int):
        """Set the target position (right-click)"""
        self.target_pos = [row, col]
        self.update_display()
    
    def update_display(self):
        """Update the visual display with target position"""
        for r in range(8):
            for c in range(8):
                btn = self.buttons[r][c]
                
                if [r, c] == self.target_pos:
                    # Target position
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #ff9800;
                            color: white;
                            border: 2px solid #f57c00;
                            font-weight: bold;
                        }
                    """)
                    btn.setText("🎯")
                elif self.grid[r][c]:
                    # Selected tile
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #4caf50;
                            color: white;
                            border: 1px solid #388e3c;
                        }
                    """)
                    btn.setText("")
                else:
                    # Empty tile (dark theme)
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #2d3748;
                            border: 1px solid #4a5568;
                        }
                    """)
                    btn.setText("")
    
    def get_boolean_mask(self):
        """Get the mask as boolean array"""
        return [[bool(self.grid[r][c]) for c in range(8)] for r in range(8)]
    
    def get_target_position(self):
        """Get the target position"""
        return self.target_pos[:]


class FileOperationsWidget(QWidget):
    """
    Reusable file operations widget
    Common save/load/delete functionality with customizable button sets
    """

    def __init__(self, button_set="full", parent=None):
        """
        Initialize file operations widget

        Args:
            button_set: "full" (all buttons), "basic" (new/load/save), "save_only" (save/save_as)
        """
        super().__init__(parent)
        self.button_set = button_set
        self.setup_ui()

    def setup_ui(self):
        """Setup the file operations UI"""
        layout = QHBoxLayout()

        # Create buttons based on button set
        if self.button_set in ["full", "basic"]:
            self.new_btn = QPushButton("📄 New")
            self.load_btn = QPushButton("📂 Load")

        if self.button_set in ["full", "basic", "save_only"]:
            self.save_btn = QPushButton("💾 Save")
            self.save_as_btn = QPushButton("💾 Save As...")

        if self.button_set == "full":
            self.delete_btn = QPushButton("🗑️ Delete")

        # Style the buttons with dark theme support
        button_style = """
            QPushButton {
                font-weight: bold;
                padding: 8px 12px;
                border-radius: 4px;
                border: 1px solid palette(mid);
                background-color: palette(button);
                color: palette(button-text);
            }
            QPushButton:hover {
                background-color: palette(highlight);
                border-color: palette(highlight);
                color: palette(highlighted-text);
            }
            QPushButton:pressed {
                background-color: palette(dark);
                color: palette(bright-text);
            }
        """

        # Add buttons to layout based on button set
        if self.button_set in ["full", "basic"]:
            self.new_btn.setStyleSheet(button_style)
            self.load_btn.setStyleSheet(button_style)
            layout.addWidget(self.new_btn)
            layout.addWidget(self.load_btn)

        if self.button_set in ["full", "basic", "save_only"]:
            self.save_btn.setStyleSheet(button_style)
            self.save_as_btn.setStyleSheet(button_style)
            layout.addWidget(self.save_btn)
            layout.addWidget(self.save_as_btn)

        if self.button_set == "full":
            self.delete_btn.setStyleSheet(button_style)
            layout.addWidget(self.delete_btn)

        layout.addStretch()
        self.setLayout(layout)

    def connect_signals(self, new_func: Optional[Callable] = None, load_func: Optional[Callable] = None,
                       save_func: Optional[Callable] = None, save_as_func: Optional[Callable] = None,
                       delete_func: Optional[Callable] = None):
        """Connect button signals to functions"""
        if new_func and hasattr(self, 'new_btn'):
            self.new_btn.clicked.connect(new_func)
        if load_func and hasattr(self, 'load_btn'):
            self.load_btn.clicked.connect(load_func)
        if save_func and hasattr(self, 'save_btn'):
            self.save_btn.clicked.connect(save_func)
        if save_as_func and hasattr(self, 'save_as_btn'):
            self.save_as_btn.clicked.connect(save_as_func)
        if delete_func and hasattr(self, 'delete_btn'):
            self.delete_btn.clicked.connect(delete_func)

    def set_button_enabled(self, button_name: str, enabled: bool):
        """Enable/disable specific buttons"""
        button_map = {
            'new': 'new_btn',
            'load': 'load_btn',
            'save': 'save_btn',
            'save_as': 'save_as_btn',
            'delete': 'delete_btn'
        }

        if button_name in button_map:
            btn_attr = button_map[button_name]
            if hasattr(self, btn_attr):
                getattr(self, btn_attr).setEnabled(enabled)


class ValidationStatusWidget(QWidget):
    """
    Reusable validation status display
    Shows validation results with color coding
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Setup the validation status UI"""
        layout = QVBoxLayout()
        
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: palette(base);
                color: palette(text);
                border: 1px solid palette(mid);
            }
        """)
        
        layout.addWidget(self.status_label)
        self.setLayout(layout)
    
    def show_success(self, message: str):
        """Show success status"""
        self.status_label.setText(f"✅ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }
        """)
    
    def show_error(self, message: str):
        """Show error status"""
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }
        """)
    
    def show_warning(self, message: str):
        """Show warning status"""
        self.status_label.setText(f"⚠️ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #fff3cd;
                color: #856404;
                border: 1px solid #ffeaa7;
            }
        """)
    
    def show_info(self, message: str):
        """Show info status"""
        self.status_label.setText(f"ℹ️ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }
        """)


def create_section_header(title: str, description: str = "") -> QLabel:
    """Create a consistent section header"""
    if description:
        text = f"<h3>{title}</h3><p style='color: #666; margin-top: 5px;'>{description}</p>"
    else:
        text = f"<h3>{title}</h3>"
    
    label = QLabel(text)
    label.setStyleSheet("""
        QLabel {
            padding: 10px;
            background-color: palette(base);
            color: palette(text);
            border-left: 4px solid palette(highlight);
            margin-bottom: 10px;
        }
    """)
    return label


def create_info_box(message: str, box_type: str = "info") -> QLabel:
    """Create an info box with consistent styling"""
    colors = {
        "info": {"bg": "#d1ecf1", "border": "#bee5eb", "text": "#0c5460"},
        "success": {"bg": "#d4edda", "border": "#c3e6cb", "text": "#155724"},
        "warning": {"bg": "#fff3cd", "border": "#ffeaa7", "text": "#856404"},
        "error": {"bg": "#f8d7da", "border": "#f5c6cb", "text": "#721c24"}
    }

    color = colors.get(box_type, colors["info"])

    label = QLabel(message)
    label.setWordWrap(True)
    label.setStyleSheet(f"""
        QLabel {{
            padding: 12px;
            background-color: {color['bg']};
            color: {color['text']};
            border: 1px solid {color['border']};
            border-radius: 4px;
            margin: 5px 0;
        }}
    """)

    return label


def create_legend_item(color: str, border: str, label_text: str) -> Tuple[QPushButton, QLabel]:
    """Create a legend item with consistent styling"""
    btn = QPushButton()
    btn.setFixedSize(20, 20)
    btn.setStyleSheet(f"background: {color}; border: 1px solid {border};")
    btn.setEnabled(False)

    label = QLabel(label_text)
    return btn, label


def create_dialog_buttons(save_text: str = "Save", cancel_text: str = "Cancel") -> Tuple[QPushButton, QPushButton]:
    """Create standard dialog buttons with consistent styling"""
    cancel_btn = QPushButton(cancel_text)
    save_btn = QPushButton(save_text)
    save_btn.setDefault(True)

    return save_btn, cancel_btn


def create_grid_instructions(text: str) -> QLabel:
    """Create consistent grid instruction text"""
    instructions = QLabel(text)
    instructions.setWordWrap(True)
    instructions.setStyleSheet("color: #666; font-size: 10px; padding: 5px;")
    return instructions