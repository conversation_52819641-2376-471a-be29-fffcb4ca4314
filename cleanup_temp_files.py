#!/usr/bin/env python3
# Temporary file cleanup script for Adventure Chess Creator
import os
from pathlib import Path

def cleanup_temp_files():
    """Remove temporary files identified in audit"""

    temp_files = []


    for temp_file in temp_files:
        try:
            if Path(temp_file).exists():
                os.remove(temp_file)
                print(f"Removed temporary file: {temp_file}")
            else:
                print(f"Temporary file not found: {temp_file}")
        except Exception as e:
            print(f"Error removing {temp_file}: {e}")

if __name__ == "__main__":
    cleanup_temp_files()
