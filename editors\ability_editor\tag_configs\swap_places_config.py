"""
SwapPlaces tag configuration for ability editor.
Handles swap places ability configurations with target selection and restrictions.
"""

from PyQt6.QtWidgets import (QFormLayout, QComboBox, QCheckBox, QSpinBox,
                            QWidget, QLabel)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from ui.inline_selection_widgets import InlinePieceSelector


class SwapPlacesConfig(BaseTagConfig):
    """Configuration for swapPlaces tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "swapPlaces")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for swap places configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting swap places UI creation")
            
            # Create main widget
            main_widget = QWidget()
            form_layout = QFormLayout()
            
            # Target pieces selector
            self.swap_piece_selector = InlinePieceSelector(
                parent=self.editor,
                title="Swap Targets",
                allow_costs=False
            )
            form_layout.addRow("Target Pieces:", self.swap_piece_selector)
            self.store_widget("swap_targets", self.swap_piece_selector)
            
            # Swap range
            self.swap_range_spin = QSpinBox()
            self.swap_range_spin.setRange(1, 8)
            self.swap_range_spin.setValue(1)
            self.swap_range_spin.setSuffix(" squares")
            form_layout.addRow("Swap Range:", self.swap_range_spin)
            self.store_widget("swap_range", self.swap_range_spin)
            
            # Swap type
            self.swap_type_combo = QComboBox()
            self.swap_type_combo.addItems([
                "Direct", "Teleport", "Through Pieces", "Line of Sight"
            ])
            form_layout.addRow("Swap Type:", self.swap_type_combo)
            self.store_widget("swap_type", self.swap_type_combo)
            
            # Restrictions
            self.swap_friendly_check = QCheckBox("Can swap with friendly pieces")
            self.swap_friendly_check.setChecked(True)
            form_layout.addRow("Friendly:", self.swap_friendly_check)
            self.store_widget("swap_friendly", self.swap_friendly_check)
            
            self.swap_enemy_check = QCheckBox("Can swap with enemy pieces")
            form_layout.addRow("Enemy:", self.swap_enemy_check)
            self.store_widget("swap_enemy", self.swap_enemy_check)
            
            self.swap_empty_check = QCheckBox("Can swap with empty squares")
            form_layout.addRow("Empty Squares:", self.swap_empty_check)
            self.store_widget("swap_empty", self.swap_empty_check)
            
            # Swap conditions
            self.swap_must_target_check = QCheckBox("Must target specific piece types")
            form_layout.addRow("Must Target:", self.swap_must_target_check)
            self.store_widget("swap_must_target", self.swap_must_target_check)
            
            self.swap_preserve_abilities_check = QCheckBox("Preserve piece abilities after swap")
            self.swap_preserve_abilities_check.setChecked(True)
            form_layout.addRow("Preserve Abilities:", self.swap_preserve_abilities_check)
            self.store_widget("swap_preserve_abilities", self.swap_preserve_abilities_check)
            
            # Swap effects
            self.swap_damage_check = QCheckBox("Deal damage during swap")
            form_layout.addRow("Swap Damage:", self.swap_damage_check)
            self.store_widget("swap_damage", self.swap_damage_check)
            
            self.swap_stun_check = QCheckBox("Stun swapped pieces")
            form_layout.addRow("Stun Effect:", self.swap_stun_check)
            self.store_widget("swap_stun", self.swap_stun_check)
            
            main_widget.setLayout(form_layout)
            parent_layout.addWidget(main_widget)
            
            # Connect change signals for all widgets
            self.connect_change_signals(self.swap_piece_selector)
            self.connect_change_signals(self.swap_range_spin)
            self.connect_change_signals(self.swap_type_combo)
            self.connect_change_signals(self.swap_friendly_check)
            self.connect_change_signals(self.swap_enemy_check)
            self.connect_change_signals(self.swap_empty_check)
            self.connect_change_signals(self.swap_must_target_check)
            self.connect_change_signals(self.swap_preserve_abilities_check)
            self.connect_change_signals(self.swap_damage_check)
            self.connect_change_signals(self.swap_stun_check)
            
            self.log_debug("Swap places UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting swap places data population with: {data}")
            
            # Target pieces
            piece_selector = self.get_widget_by_name("swap_targets")
            if piece_selector and "swapTargets" in data:
                piece_selector.set_pieces(data["swapTargets"])
            
            # Swap range
            range_spin = self.get_widget_by_name("swap_range")
            if range_spin and "swapRange" in data:
                range_spin.setValue(data["swapRange"])
            
            # Swap type
            type_combo = self.get_widget_by_name("swap_type")
            if type_combo and "swapType" in data:
                swap_type = data["swapType"]
                index = type_combo.findText(swap_type)
                if index >= 0:
                    type_combo.setCurrentIndex(index)
            
            # Restrictions
            friendly_check = self.get_widget_by_name("swap_friendly")
            if friendly_check and "swapFriendly" in data:
                friendly_check.setChecked(data["swapFriendly"])
            
            enemy_check = self.get_widget_by_name("swap_enemy")
            if enemy_check and "swapEnemy" in data:
                enemy_check.setChecked(data["swapEnemy"])
            
            empty_check = self.get_widget_by_name("swap_empty")
            if empty_check and "swapEmpty" in data:
                empty_check.setChecked(data["swapEmpty"])
            
            # Conditions
            must_target_check = self.get_widget_by_name("swap_must_target")
            if must_target_check and "swapMustTarget" in data:
                must_target_check.setChecked(data["swapMustTarget"])
            
            preserve_abilities_check = self.get_widget_by_name("swap_preserve_abilities")
            if preserve_abilities_check and "swapPreserveAbilities" in data:
                preserve_abilities_check.setChecked(data["swapPreserveAbilities"])
            
            # Effects
            damage_check = self.get_widget_by_name("swap_damage")
            if damage_check and "swapDamage" in data:
                damage_check.setChecked(data["swapDamage"])
            
            stun_check = self.get_widget_by_name("swap_stun")
            if stun_check and "swapStun" in data:
                stun_check.setChecked(data["swapStun"])
            
            self.log_debug("Swap places data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the swap places configuration data
        """
        try:
            data = {}
            
            # Target pieces
            piece_selector = self.get_widget_by_name("swap_targets")
            if piece_selector:
                data["swapTargets"] = piece_selector.get_pieces()
            
            # Swap range
            range_spin = self.get_widget_by_name("swap_range")
            if range_spin:
                data["swapRange"] = range_spin.value()
            
            # Swap type
            type_combo = self.get_widget_by_name("swap_type")
            if type_combo:
                data["swapType"] = type_combo.currentText()
            
            # Restrictions
            friendly_check = self.get_widget_by_name("swap_friendly")
            if friendly_check:
                data["swapFriendly"] = friendly_check.isChecked()
            
            enemy_check = self.get_widget_by_name("swap_enemy")
            if enemy_check:
                data["swapEnemy"] = enemy_check.isChecked()
            
            empty_check = self.get_widget_by_name("swap_empty")
            if empty_check:
                data["swapEmpty"] = empty_check.isChecked()
            
            # Conditions
            must_target_check = self.get_widget_by_name("swap_must_target")
            if must_target_check:
                data["swapMustTarget"] = must_target_check.isChecked()
            
            preserve_abilities_check = self.get_widget_by_name("swap_preserve_abilities")
            if preserve_abilities_check:
                data["swapPreserveAbilities"] = preserve_abilities_check.isChecked()
            
            # Effects
            damage_check = self.get_widget_by_name("swap_damage")
            if damage_check:
                data["swapDamage"] = damage_check.isChecked()
            
            stun_check = self.get_widget_by_name("swap_stun")
            if stun_check:
                data["swapStun"] = stun_check.isChecked()
            
            self.log_debug(f"Collected swap places data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
