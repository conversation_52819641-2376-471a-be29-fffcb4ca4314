{"version": "1.0.0", "name": "Queens sumon", "description": " Can summon 1 pawn withen 1 range on her side of the map", "cost": 2, "tags": ["summon", "range"], "_dialogStates": {"piece_selector": {"piece_selection": "Any", "cost_value": 1, "no_cost_checked": false}, "ability_selector": {"selected_abilities": [], "cost_value": 1, "no_cost_checked": false, "search_text": ""}}, "autoCost": true, "individualCosts": ["Summon: Friendly Adventure Pawn (1)", "Range Pattern: 8 tiles (1)"], "autoCalculateCost": true, "displaceCustomMap": null, "activationMode": "click", "rangeMask": [[false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false], [false, false, false, true, true, true, false, false], [false, false, false, true, false, true, false, false], [false, false, false, true, true, true, false, false], [false, false, false, false, false, false, false, false], [false, false, false, false, false, false, false, false]], "piecePosition": [4, 4], "rangeFriendlyOnly": true, "rangeEnemyOnly": false, "summonMax": 1, "summonList": [{"piece": "Friendly Adventure Pawn", "cost": 1}]}